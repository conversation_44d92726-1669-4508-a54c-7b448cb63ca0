using System.Collections.Generic;

namespace Modules.Analytics
{
    public interface IAnalyticsClient
    {
        long SessionCount { get; }
        void Initialize();
        void SetUserId(string userId, AnalyticsSink sink = AnalyticsSink.All);
        void SetUserProperty(string key, string value, AnalyticsSink sink = AnalyticsSink.All);
        void Track(string eventName, Dictionary<string, object> properties, AnalyticsSink sink = AnalyticsSink.All);
        void Track(string eventName, AnalyticsSink sink = AnalyticsSink.All);
    }
}