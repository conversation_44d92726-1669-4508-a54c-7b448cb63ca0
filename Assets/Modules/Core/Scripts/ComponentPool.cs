using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;
using VContainer;
using VContainer.Unity;
using Object = UnityEngine.Object;

namespace Modules.Core
{
    public interface IPoolItemReturnable
    {
        void ReturnToPool();
    }

    public class ComponentPool<TPoolItem> where TPoolItem : Component
    {
        private readonly TPoolItem prefab;
        private readonly Transform parent;
        private readonly IObjectPool<TPoolItem> pool;
        private readonly List<TPoolItem> activePoolItems;
        private readonly IObjectResolver objectResolver;

        public List<TPoolItem> ActivePoolItems => activePoolItems;

        public ComponentPool(TPoolItem prefab, Transform parent, int preloadCount = 10) : this(prefab, parent, null, preloadCount)
        {
        }

        public ComponentPool(TPoolItem prefab, Transform parent, IObjectResolver objectResolver, int preloadCount = 10)
        {
            this.prefab = prefab;
            this.parent = parent;
            this.objectResolver = objectResolver;
            pool = new ObjectPool<TPoolItem>(CreatePoolItem, GetPoolItem, ReleasePoolItem, DestroyPoolItem, false, preloadCount);
            activePoolItems = new List<TPoolItem>(preloadCount);

            Preload(preloadCount);
        }

        public virtual TPoolItem Get()
        {
            var poolItem = pool.Get();
            activePoolItems.Add(poolItem);
            return poolItem;
        }

        public virtual void Release(TPoolItem poolItem)
        {
            if (poolItem == null || !activePoolItems.Contains(poolItem))
            {
                return;
            }

            if (poolItem.TryGetComponent(out IPoolItemReturnable returnPoolItem))
            {
                returnPoolItem.ReturnToPool();
            }

            activePoolItems.Remove(poolItem);
            pool.Release(poolItem);
        }

        public virtual void ReleaseAll(Action<TPoolItem> onBeforeRelease = null)
        {
            for (var i = activePoolItems.Count - 1; i >= 0; i--)
            {
                var poolItem = activePoolItems[i];
                onBeforeRelease?.Invoke(poolItem);
                Release(poolItem);
            }
        }

        public virtual void Clear(bool isDestroyItems = true)
        {
            activePoolItems.Clear();
            if (isDestroyItems)
            {
                pool.Clear();
            }
        }

        protected virtual TPoolItem CreatePoolItem()
        {
            return objectResolver == null ? Object.Instantiate(prefab, parent) : objectResolver.Instantiate(prefab, parent);
        }

        protected virtual void GetPoolItem(TPoolItem poolItem)
        {
            poolItem.gameObject.SetActive(true);
        }

        protected virtual void ReleasePoolItem(TPoolItem poolItem)
        {
            poolItem.gameObject.SetActive(false);
            poolItem.transform.SetParent(parent, false);
        }

        protected virtual void DestroyPoolItem(TPoolItem poolItem)
        {
            if (poolItem == null || poolItem.gameObject == null)
            {
                return;
            }

            Object.Destroy(poolItem.gameObject);
        }

        private void Preload(int preloadCount)
        {
            var buffer = new List<TPoolItem>(preloadCount);
            for (var i = 0; i < preloadCount; i++)
            {
                var item = Get();
                if (item == null)
                {
                    continue;
                }

                buffer.Add(item);
            }

            for (var i = 0; i < preloadCount; i++)
            {
                Release(buffer[i]);
            }
        }
    }
}