using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;

namespace Modules.XR
{
    [RequireComponent(typeof(Rigidbody))]
    public class XRSimpleGrabInteractable : XRGrabInteractable
    {
        [Header("XR Grab Interactable")]
        [SerializeField] private Rigidbody selfRigidbody;
        [SerializeField] private Transform leftHandAttachNode;
        [SerializeField] private Transform rightHandAttachNode;

        public SelectEnterEvent BeforeSelectEntering { get; } = new();
        public SelectEnterEvent SelectEntering { get; } = new();
        public SelectExitEvent BeforeSelectExiting { get; } = new();
        public SelectExitEvent SelectExiting { get; } = new();
        public Rigidbody Rigidbody => selfRigidbody;

        public Transform LeftHandAttachNode
        {
            get => leftHandAttachNode;
            set => leftHandAttachNode = value;
        }

        public Transform RightHandAttachNode
        {
            get => rightHandAttachNode;
            set => rightHandAttachNode = value;
        }

        protected override void Awake()
        {
            interactionManager = XRInteractionManagerProvider.XRInteractionManager;
            base.Awake();
        }

        protected override void OnSelectEntering(SelectEnterEventArgs args)
        {
            attachTransform = args.interactorObject.IsLeftHand() ? leftHandAttachNode : rightHandAttachNode;
            BeforeSelectEntering.Invoke(args);
            base.OnSelectEntering(args);
            SelectEntering.Invoke(args);
        }

        protected override void OnSelectExiting(SelectExitEventArgs args)
        {
            BeforeSelectExiting.Invoke(args);
            base.OnSelectExiting(args);
            SelectExiting.Invoke(args);
        }

        public void RegisterInteractable()
        {
            interactionManager.RegisterInteractable((IXRInteractable)this);
        }

        public void UnregisterInteractable()
        {
            interactionManager.UnregisterInteractable((IXRInteractable)this);
        }

        public void Grab(IXRSelectInteractor interactor)
        {
            interactionManager.SelectEnter(interactor, this);
        }

        public void Ungrab()
        {
            for (var i = interactorsSelecting.Count - 1; i >= 0; i--)
            {
                interactionManager.SelectCancel(interactorsSelecting[i], this);
            }
        }
    }
}