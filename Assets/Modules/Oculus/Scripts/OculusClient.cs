using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Analytics;
using Modules.Core;
using Oculus.Platform;
using Oculus.Platform.Models;
using UnityEngine;
using UnityEngine.SceneManagement;
using Application = UnityEngine.Application;
using Error = Modules.Core.Error;
using PlatformCore = Oculus.Platform.Core;
using Result = Modules.Core.Result;

namespace Modules.Oculus
{
    internal class OculusClient : IOculusClient
    {
        private const string UserCanceledPurchaseValue = "user_canceled";

        private ulong userIdInt;

        private readonly IAnalyticsClient analyticsClient;
        private readonly ISubject<Unit> onInvitePanelOpened = new Subject<Unit>();
        private readonly ISubject<List<string>> onUsersInvited = new Subject<List<string>>();

        public string UserId { get; private set; }
        public string UserName { get; private set; }
        public string OrgScopedUserId { get; private set; }
        public string Age { get; private set; }

        public IObservable<Unit> OnInvitePanelOpened => onInvitePanelOpened;
        public IObservable<List<string>> OnUsersInvited => onUsersInvited;

        public OculusClient(IAnalyticsClient analyticsClient)
        {
            this.analyticsClient = analyticsClient;
        }

        public async UniTask<Result> Initialize(CancellationToken cancellationToken = default)
        {
            try
            {
                if (!PlatformCore.IsInitialized())
                {
                    await PlatformCore.AsyncInitialize().ToUniTask(cancellationToken);
                }

                await Entitlements.IsUserEntitledToApplication().ToUniTask(cancellationToken);

                if (string.IsNullOrEmpty(UserId))
                {
                    var loggedInUserRequest = await Users.GetLoggedInUser().ToUniTask(cancellationToken);
                    userIdInt = loggedInUserRequest.Data.ID;
                    UserId = userIdInt.ToString();
                    UserName = loggedInUserRequest.Data.OculusID;
                }

                if (string.IsNullOrEmpty(OrgScopedUserId))
                {
                    var orgScopedIdRequest = await Users.GetOrgScopedID(userIdInt).ToUniTask(cancellationToken);
                    OrgScopedUserId = orgScopedIdRequest.Data.ID.ToString();
                }

                if (string.IsNullOrEmpty(Age))
                {
                    var userAgeCategoryRequest = await UserAgeCategory.Get().ToUniTask(cancellationToken);
                    Age = userAgeCategoryRequest.Data.AgeCategory.ToString();
                }

                Logger.Oculus.Debug("UserId: {0}, UserName: {1}, OrgScopedUserId: {2}, Age: {3}", UserId, UserName, OrgScopedUserId, Age);
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult(new Error("Initialize", "Operation canceled"));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult(new Error("Initialize", exception.Message));
            }

            return Result.Ok();
        }

        public async UniTask<Result<string>> GetNonce(CancellationToken cancellationToken = default)
        {
            try
            {
                var userProofRequest = await Users.GetUserProof().ToUniTask(cancellationToken);
                var nonce = userProofRequest.Data.Value;
                Logger.Oculus.Debug("Nonce retrieved successfully");

                return Result<string>.Ok(nonce);
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult<string>(new Error("GetNonce", "Operation canceled"));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult<string>(new Error("GetNonce", exception.Message));
            }
        }

        public async UniTask<Result<List<Product>>> GetProductList(string[] skus, CancellationToken cancellationToken = default)
        {
            try
            {
                var message = await IAP.GetProductsBySKU(skus).ToUniTask(cancellationToken);
                var productList = GetProductList(message.GetProductList());
                Logger.Oculus.Debug("Products retrieved successfully: {0}", string.Join(',', productList));

                return Result<List<Product>>.Ok(productList);
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult<List<Product>>(new Error("GetProductList", "Operation canceled"));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult<List<Product>>(new Error("GetProductList", exception.Message));
            }
        }

        public async UniTask<Result<List<Product>>> GetPurchaseList(CancellationToken cancellationToken = default)
        {
            try
            {
                var message = await IAP.GetViewerPurchases().ToUniTask(cancellationToken);
                var productList = GetProductList(message.GetProductList());
                Logger.Oculus.Debug("Purchases retrieved successfully: {0}", string.Join(',', productList));

                return Result<List<Product>>.Ok(productList);
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult<List<Product>>(new Error("GetPurchaseList", "Operation canceled"));
            }
            catch (Exception exception)
            {
                return LogFatalAndGetResult<List<Product>>(new Error("GetPurchaseList", exception.Message));
            }
        }

        public async UniTask<Result> Purchase(string sku, bool isConsumable, CancellationToken cancellationToken = default)
        {
            if (Application.isEditor)
            {
                return Result.Ok();
            }

            try
            {
                await IAP.LaunchCheckoutFlow(sku).ToUniTask(cancellationToken);
                Logger.Oculus.Debug("Purchase [{0}] completed successfully", sku);
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult(new Error("Purchase", "Operation canceled"));
            }
            catch (Exception exception)
            {
                var messageObject = ParsePurchaseErrorMessage(exception.Message);
                if (messageObject == null)
                {
                    Logger.Oculus.Fatal(new OculusPurchaseException(exception.Message));
                }
                else if (messageObject.category != UserCanceledPurchaseValue)
                {
                    var parameters = new Dictionary<string, object>
                    {
                        { "sku", sku },
                        { "code", messageObject.code },
                        { "reason", messageObject.category }
                    };
                    analyticsClient.Track("oculus_purchase_error", parameters);
                    Logger.Oculus.Fatal(new OculusPurchaseException(exception.Message));
                }

                return Result.Fail(new Error("Purchase", exception.Message));
            }

            if (isConsumable)
            {
                return await ConsumePurchase(sku, "after_purchase", cancellationToken);
            }

            return Result.Ok();
        }

        public async UniTask<Result> ConsumePurchase(string sku, string source, CancellationToken cancellationToken = default)
        {
            if (Application.isEditor)
            {
                return Result.Ok();
            }

            Result result;
            var retryCount = 0;

            do
            {
                result = await ConsumePurchaseInternal(sku, cancellationToken);
                if (result.IsFail)
                {
                    await UniTask.Yield(cancellationToken);
                }
            } while (result.IsFail && retryCount++ < 3);

            if (result.IsFail)
            {
                analyticsClient.Track("oculus_consume_error", new Dictionary<string, object>
                {
                    { "sku", sku },
                    { "source", source }
                });
            }

            return result;
        }

        public void SetPresence(string sessionId)
        {
            if (Application.isEditor)
            {
                return;
            }

            try
            {
                var options = new GroupPresenceOptions();
                options.SetDestinationApiName(SceneManager.GetActiveScene().name);
                options.SetLobbySessionId(sessionId);
                options.SetMatchSessionId("remio");
                options.SetIsJoinable(true);
                GroupPresence.Set(options).ToUniTask().Forget();
            }
            catch (Exception exception)
            {
                Logger.Oculus.Fatal(new OculusException(new Error("SetPresence", exception.Message).ToString()));
            }
        }

        public void LaunchInvitePanel()
        {
            if (Application.isEditor)
            {
                return;
            }

            try
            {
                LaunchInvitePanelInternal().Forget();
            }
            catch (Exception exception)
            {
                Logger.Oculus.Fatal(new OculusException(new Error("LaunchInvitePanel", exception.Message).ToString()));
            }
        }

        private async UniTask<Result> ConsumePurchaseInternal(string sku, CancellationToken cancellationToken = default)
        {
            try
            {
                await IAP.ConsumePurchase(sku).ToUniTask(cancellationToken);
                Logger.Oculus.Debug("Purchase [{0}] consumed successfully", sku);
                return Result.Ok();
            }
            catch (OperationCanceledException)
            {
                return LogWarnAndGetResult(new Error("ConsumePurchase", "Operation canceled"));
            }
            catch (Exception exception)
            {
                var error = new Error("ConsumePurchase", exception.Message);
                Logger.Oculus.Fatal(new OculusConsumeException(error.ToString()));
                return Result.Fail(error);
            }
        }

        private PurchaseErrorMessage ParsePurchaseErrorMessage(string message)
        {
            try
            {
                return JsonUtility.FromJson<PurchaseErrorMessage>(message);
            }
            catch (Exception)
            {
                return null;
            }
        }

        private async UniTaskVoid LaunchInvitePanelInternal()
        {
            await GroupPresence.LaunchInvitePanel(new InviteOptions()).ToUniTask();
            GroupPresence.SetInvitationsSentNotificationCallback(OnSetInvitationsSentNotificationCallback);
            onInvitePanelOpened.OnNext(Unit.Default);
        }

        private void OnSetInvitationsSentNotificationCallback(Message<LaunchInvitePanelFlowResult> message)
        {
            if (message.IsError)
            {
                Logger.Oculus.Error("Error sending invites: {0}", message.GetError().Message);
            }
            else
            {
                var invitedUsers = new List<string>();
                foreach (var user in message.Data.InvitedUsers)
                {
                    invitedUsers.Add(user.DisplayName);
                }

                Logger.Oculus.Debug("Invited users: {0}", string.Join(',', invitedUsers));
                onUsersInvited.OnNext(invitedUsers);
            }
        }

        private static List<Product> GetProductList(ProductList productList)
        {
            var storeProductList = new List<Product>();

            if (productList == null)
            {
                return storeProductList;
            }

            foreach (var product in productList)
            {
                storeProductList.Add(new Product(product.Sku, product.Name, product.FormattedPrice, product.Type == ProductType.CONSUMABLE));
            }

            return storeProductList;
        }

        private static Result<T> LogFatalAndGetResult<T>(Error error)
        {
            Logger.Oculus.Fatal(new OculusException(error.ToString()));
            return Result<T>.Fail(error);
        }

        private static Result LogFatalAndGetResult(Error error)
        {
            Logger.Oculus.Fatal(new OculusException(error.ToString()));
            return Result.Fail(error);
        }

        private static Result<T> LogWarnAndGetResult<T>(Error error)
        {
            Logger.Oculus.Warn(error.ToString());
            return Result<T>.Fail(error);
        }

        private static Result LogWarnAndGetResult(Error error)
        {
            Logger.Oculus.Warn(error.ToString());
            return Result.Fail(error);
        }
    }
}