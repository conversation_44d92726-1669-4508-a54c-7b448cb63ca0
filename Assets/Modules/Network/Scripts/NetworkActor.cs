using System;
using System.Threading;
using Fusion;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Modules.Network
{
    public class NetworkActor : NetworkBehaviour, IAfterSpawned
    {
        [Header("Network Actor")]
        [SerializeField] private InjectionType injectionType;

        private CancellationTokenSource despawnCancellationTokenSource;

        protected bool IsInjected { get; private set; }
        protected bool IsSpawnedHappened { get; private set; }

        public PlayerRef StateAuthority => Object == null ? PlayerRef.None : Object.StateAuthority;
        public int PlayerId => StateAuthority.PlayerId;
        public bool IsShutdown => Runner == null || Runner.IsShutdown;

        public CancellationToken DespawnCancellationToken
        {
            get
            {
                despawnCancellationTokenSource ??= new CancellationTokenSource();
                return despawnCancellationTokenSource.Token;
            }
        }

        [Inject]
        private void ConstructInternal()
        {
            if (IsInjected)
            {
                Logger.Network.Warn("NetworkActor [{0}] already injected", GetType().Name);
            }

            IsInjected = true;
        }

        protected virtual void Awake()
        {
            NetworkActorDispatcher.Inject(injectionType, this);
        }

        public override void Spawned()
        {
            base.Spawned();
            IsSpawnedHappened = true;
        }

        public virtual void AfterSpawned()
        {
            NetworkActorDispatcher.Spawned(this);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            despawnCancellationTokenSource.CancelAndDispose();
            despawnCancellationTokenSource = null;

            NetworkActorDispatcher.Despawned(this);
        }

        public virtual void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        protected void SendRpcSafe(Action callback)
        {
            if (IsShutdown)
            {
                return;
            }

            callback?.Invoke();
        }
    }
}