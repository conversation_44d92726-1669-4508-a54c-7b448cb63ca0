using Cysharp.Threading.Tasks;
using Fusion;
using Sirenix.OdinInspector;

namespace Modules.Network
{
    internal class MasterClientHandler : NetworkActor
    {
        private readonly IAsyncReactiveProperty<PlayerRef> masterClient = new AsyncReactiveProperty<PlayerRef>(PlayerRef.None);

        public IReadOnlyAsyncReactiveProperty<PlayerRef> MasterClient => masterClient;

        public override void Spawned()
        {
            UpdateMasterClient();
        }

        public override void Render()
        {
            base.Render();
            UpdateMasterClient();
        }

        [Button]
        public void SetMeMasterClient()
        {
            SendRpcSafe(() => SetMasterClientRpc());
        }

        private void UpdateMasterClient()
        {
            if (masterClient.Value == StateAuthority)
            {
                return;
            }

            Logger.Network.Debug("Master Client changed: {0}", StateAuthority.PlayerId);
            masterClient.Value = StateAuthority;
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void SetMasterClientRpc(RpcInfo info = default)
        {
            Runner.SetMasterClient(info.Source);
        }
    }
}