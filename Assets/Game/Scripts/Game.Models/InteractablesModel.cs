using System;
using System.Reactive.Subjects;
using Game.Views.Consumeables;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Modules.Core;
using Modules.Network;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Models
{
    public class InteractablesModel : ModelBase
    {
        private INetworkClient networkClient;
        private SharedInteractableSettings sharedInteractableSettings;

        private readonly ISubject<CreateInteractableArgs> onInteractableCreating = new Subject<CreateInteractableArgs>();

        public bool CanCreateNewInteractable => GetLocalPlayerInteractableCount() <= sharedInteractableSettings.MaxInteractablesPerPlayer;
        public IObservable<CreateInteractableArgs> OnInteractableCreating => onInteractableCreating;

        [Inject]
        private void Construct(INetworkClient networkClient, SharedInteractableSettings sharedInteractableSettings)
        {
            this.networkClient = networkClient;
            this.sharedInteractableSettings = sharedInteractableSettings;
        }

        public void CreateInteractable(string code, IXRSelectInteractor interactor, bool playSound = false)
        {
            onInteractableCreating.OnNext(new CreateInteractableArgs(code, interactor, playSound));
        }

        private int GetLocalPlayerInteractableCount()
        {
            var count = 0;
            if (networkClient.TryGetNetworkActorList<InteractableActor>(out var result))
            {
                foreach (var interactableActor in result)
                {
                    if (interactableActor.HasStateAuthority
                        && interactableActor is not ConsumableActor
                        && interactableActor is not OreActor)
                    {
                        count++;
                    }
                }
            }

            return count;
        }
    }
}