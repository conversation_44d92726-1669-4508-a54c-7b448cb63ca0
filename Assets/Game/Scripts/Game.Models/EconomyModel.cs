using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Services;
using Game.Services.Data;
using Game.Views.Avatars;
using Game.Views.Interactables;
using Modules.Core;

namespace Game.Models
{
    public class EconomyModel : ModelBase
    {
        private readonly AvatarsConfig avatarsConfig;
        private readonly IEconomyService economyService;
        private readonly IAnalyticsService analyticsService;
        private readonly InteractablesConfig interactablesConfig;
        private readonly InteractablesSorter interactablesSorter;
        private readonly ISubject<PurchaseResultData> onPurchaseCompleted = new Subject<PurchaseResultData>();
        private readonly ISubject<int> onDiamondsReceived = new Subject<int>();

        public List<ShopItemData> ShopItemList { get; } = new();
        public List<ShopInventoryData> InventoryList { get; } = new();
        public IReadOnlyAsyncReactiveProperty<int> CoinAmount => economyService.CoinAmount;
        public IReadOnlyAsyncReactiveProperty<int> DiamondAmount => economyService.DiamondAmount;
        public IObservable<PurchaseResultData> OnPurchaseCompleted => onPurchaseCompleted;
        public IObservable<int> OnDiamondsReceived => onDiamondsReceived;

        public EconomyModel(IEconomyService economyService, IAnalyticsService analyticsService, AvatarsConfig avatarsConfig, InteractablesConfig interactablesConfig)
        {
            this.avatarsConfig = avatarsConfig;
            this.economyService = economyService;
            this.analyticsService = analyticsService;
            this.interactablesConfig = interactablesConfig;
            interactablesSorter = new InteractablesSorter(interactablesConfig);
        }

        public async UniTask Initialize(CancellationToken cancellationToken)
        {
            var currentGroup = GetCurrentShopGroup();
            var result = await economyService.GetEconomyData(currentGroup, cancellationToken);

            if (result.IsFail)
            {
                return;
            }

            ShopItemList.Clear();
            InventoryList.Clear();

            ShopItemList.AddRange(GetValidatedShopItemList(result.Value.shopItemList));
            InventoryList.AddRange(GetValidatedInventoryList(result.Value.inventoryList));

            GameLogger.Economy.Debug("Current shop group: {0}", GetCurrentShopGroup());
        }

        public async UniTask<Result<PurchaseResultData>> Purchase(ShopItemData item, string source, CancellationToken cancellationToken)
        {
            var result = await economyService.Purchase(item, cancellationToken);
            return ResolvePurchase(result, item, source, cancellationToken);
        }

        public async UniTask<Result> ClaimDailyReward(CancellationToken cancellationToken)
        {
            var result = await RedeemPromoCode("CLAIM_DAILY_REWARD", cancellationToken);
            if (result.IsOk)
            {
                await UpdateCoinAmount(cancellationToken);
            }

            return result;
        }

        public async UniTask<Result<PurchaseResultData>> RedeemPromoCode(string promoCode, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(promoCode))
            {
                return Result<PurchaseResultData>.Fail(new Error("RedeemPromoCode", "Promo code is not valid"));
            }

            var purchaseId = promoCode.ToUpper();
            var shopItem = ShopItemList.FindAll(i => i.IsPromoCodeItem).Find(i => i.purchaseId == purchaseId);
            var hasShopItem = shopItem != null;

            if (!hasShopItem)
            {
                return Result<PurchaseResultData>.Fail(new Error("RedeemPromoCode", "Promo code is not valid"));
            }

            if (shopItem.isPurchased)
            {
                return Result<PurchaseResultData>.Fail(new Error("RedeemPromoCode", "Promo code already redeemed"));
            }

            var result = await economyService.PurchaseById(purchaseId, cancellationToken);
            return ResolvePurchase(result, shopItem, AnalyticsSource.PromoScreen, cancellationToken);
        }

        public async UniTask<Result> AddCoinAmount(int coinAmount, CancellationToken cancellationToken)
        {
            return await economyService.AddCoinAmount(coinAmount, cancellationToken);
        }

        public async UniTask<Result> UpdateCoinAmount(CancellationToken cancellationToken)
        {
            return await economyService.UpdateCoinAmount(cancellationToken);
        }

        public async UniTask<Result> AddDiamondAmount(int diamondAmount, CancellationToken cancellationToken)
        {
            var result = await economyService.AddDiamondAmount(diamondAmount, cancellationToken);
            if (diamondAmount > 0 && result.IsOk)
            {
                onDiamondsReceived.OnNext(diamondAmount);
            }

            return result;
        }

        public async UniTask<Result> UpdateDiamondAmount(CancellationToken cancellationToken)
        {
            return await economyService.UpdateDiamondAmount(cancellationToken);
        }

        public List<string> GetOwnedInteractableList()
        {
            return interactablesSorter.GetSortedList(InventoryList.FindAll(i => i.IsOwnedInteractable));
        }

        public void AddBadgeInventoryList(List<ShopInventoryData> badgeInventoryList)
        {
            if (badgeInventoryList == null || badgeInventoryList.Count == 0)
            {
                return;
            }

            InventoryList.AddRange(badgeInventoryList);
        }

        public bool IsInventoryOwned(string code)
        {
            return InventoryList.Exists(i => i.viewCode == code && i.IsOwned);
        }

        private int GetCurrentShopGroup()
        {
            const int maxGroups = 2;
            const int duration = 7;
            const int totalDuration = maxGroups * duration;

            var currentDay = GameDateTime.Now.Day;
            var snappedTotalDuration = currentDay >= totalDuration ? currentDay % totalDuration : currentDay;
            var day = 1 + snappedTotalDuration / duration;

            return day;
        }

        private Result<PurchaseResultData> ResolvePurchase(Result<PurchaseResult> result, ShopItemData shopItem, string source, CancellationToken cancellationToken)
        {
            if (result.IsFail)
            {
                return Result<PurchaseResultData>.Fail(result.Error);
            }

            var purchaseResultData = GetPurchaseResultData(result.Value);
            ApplyPurchase(shopItem.purchaseId);
            UpdateCoinAmount(cancellationToken).Forget();
            TrackPurchaseAnalytics(shopItem, purchaseResultData, source);
            onPurchaseCompleted.OnNext(purchaseResultData);
            return Result<PurchaseResultData>.Ok(purchaseResultData);
        }

        private void ApplyPurchase(string purchaseId)
        {
            var shopItem = ShopItemList.Find(i => i.purchaseId == purchaseId);
            if (shopItem == null || shopItem.isConsumable)
            {
                return;
            }

            shopItem.SetPurchaseState(true);
        }

        private List<ShopInventoryData> GetValidatedInventoryList(List<ShopInventoryData> inventoryList)
        {
            var avatarList = inventoryList
                .FindAll(i => i.IsAvatar)
                .FindAll(i => avatarsConfig.HasAvatar(i.viewCode));

            var hatList = inventoryList
                .FindAll(i => i.IsHat)
                .FindAll(i => avatarsConfig.HasHat(i.viewCode));

            var suitList = inventoryList
                .FindAll(i => i.IsSuit)
                .FindAll(i => avatarsConfig.HasSuit(i.viewCode));

            var interactableList = inventoryList
                .FindAll(i => i.IsInteractable)
                .FindAll(i => interactablesConfig.Has(i.viewCode));

            var result = new List<ShopInventoryData>();
            result.AddRange(avatarList);
            result.AddRange(hatList);
            result.AddRange(suitList);
            result.AddRange(interactableList);
            return result;
        }

        private List<ShopItemData> GetValidatedShopItemList(List<ShopItemData> shopItemList)
        {
            var validatedShopItemList = new List<ShopItemData>();
            foreach (var shopItem in shopItemList)
            {
                shopItem.InventoryList = GetValidatedInventoryList(shopItem.InventoryList);
                if (shopItem.coinAmount > 0 || shopItem.InventoryList.Count > 0)
                {
                    validatedShopItemList.Add(shopItem);
                }
                else
                {
#if UNITY_EDITOR
                    GameLogger.Economy.Warn("Shop item {0} has no inventory", shopItem.title);
#endif
                }
            }

            return validatedShopItemList;
        }

        private PurchaseResultData GetPurchaseResultData(PurchaseResult purchaseResult)
        {
            var rewardCoinAmount = 0;
            var rewardInventoryList = new List<ShopInventoryData>();

            foreach (var rewardItem in purchaseResult.rewardItemList)
            {
                if (string.Equals(rewardItem.id, Constants.CoinId, StringComparison.InvariantCultureIgnoreCase))
                {
                    rewardCoinAmount += rewardItem.amount;
                }
                else if (TryGetInventory(rewardItem.id, out var inventory))
                {
                    rewardInventoryList.Add(inventory);
                }
            }

            return new PurchaseResultData(rewardCoinAmount, rewardInventoryList);
        }

        private bool TryGetInventory(string id, out ShopInventoryData inventory)
        {
            inventory = InventoryList.Find(i => i.id == id);
            return inventory != null;
        }

        private void TrackPurchaseAnalytics(ShopItemData shopItem, PurchaseResultData purchaseResult, string source)
        {
            var title = shopItem.title.ToLower();
            var coinReceived = purchaseResult.rewardCoinAmount;
            var coinSpent = 0;
            var category = "none";
            var usdSpent = 0f;

            if (shopItem.IsSimpleItem)
            {
                if (shopItem.FirstInventory != null && shopItem.FirstInventory.category != InventoryCategory.None)
                {
                    category = shopItem.FirstInventory.category.ToString();
                }

                coinSpent = shopItem.coinPrice;
            }
            else if (shopItem.IsOutfitItem)
            {
                category = "outfit";
                coinSpent = shopItem.coinPrice;
            }
            else if (shopItem.IsBundleItem)
            {
                usdSpent = shopItem.usdPrice;
                category = "bundle";
            }
            else if (shopItem.IsCoinItem)
            {
                usdSpent = shopItem.usdPrice;
                category = "coins";
            }
            else if (shopItem.IsPromoCodeItem)
            {
                category = "promoCode";
            }

            analyticsService.Purchase(title, category, usdSpent, coinSpent, coinReceived, source);
        }
    }
}