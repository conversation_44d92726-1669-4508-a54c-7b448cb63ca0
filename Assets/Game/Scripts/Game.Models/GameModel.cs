using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Services;
using Game.Views.Storage;
using Modules.Core;

namespace Game.Models
{
    public class GameModel : ModelBase
    {
        private readonly ISubject<Unit> onBeforeInitialized = new Subject<Unit>();
        private readonly IAsyncReactiveProperty<bool> isInitialized = new AsyncReactiveProperty<bool>(false);
        private readonly IAsyncReactiveProperty<DateTimeOffset> prisonTimeEnd = new AsyncReactiveProperty<DateTimeOffset>(DateTimeOffset.MinValue);
        private readonly IAsyncReactiveProperty<DateTimeOffset> muteTimeEnd = new AsyncReactiveProperty<DateTimeOffset>(DateTimeOffset.MinValue);

        private readonly AppConfig appConfig;
        private readonly GameConfig gameConfig;
        private readonly IGameService gameService;
        private readonly ILocalStorage localStorage;

        public string UserName { get; private set; }
        public string UserId { get; private set; }
        public List<UserLabel> UserLabelList { get; } = new();
        public string LobbyName => string.IsNullOrEmpty(gameConfig.PrivateLobbyName) ? appConfig.GameApiName : $"{appConfig.GameApiName}{gameConfig.PrivateLobbyName}";
        public DateTimeOffset GiftClaimTime { get; private set; } = DateTimeOffset.MinValue;
        public int GameCount => localStorage.GameCount;
        public IObservable<Unit> OnBeforeInitialized => onBeforeInitialized;
        public IReadOnlyAsyncReactiveProperty<bool> IsInitialized => isInitialized;
        public IReadOnlyAsyncReactiveProperty<DateTimeOffset> MuteTimeEnd => muteTimeEnd;
        public IReadOnlyAsyncReactiveProperty<DateTimeOffset> PrisonTimeEnd => prisonTimeEnd;
        public IReadOnlyAsyncReactiveProperty<int> XpAmount => gameService.XpAmount;

        public GameModel(IGameService gameService, GameConfig gameConfig, ILocalStorage localStorage, AppConfig appConfig)
        {
            this.appConfig = appConfig;
            this.gameService = gameService;
            this.gameConfig = gameConfig;
            this.localStorage = localStorage;
        }

        public async UniTask Initialize(CancellationToken disposeCancellationToken)
        {
            if (isInitialized.Value)
            {
                return;
            }

            var initialGameData = await gameService.Initialize(disposeCancellationToken);

            UserId = initialGameData.userId;
            UserName = initialGameData.userName;

            UserLabelList.Clear();
            UserLabelList.AddRange(initialGameData.userLabels);

            if (DateTimeOffset.TryParse(initialGameData.giftClaimTime, out var giftClaimTime))
            {
                GiftClaimTime = giftClaimTime;
            }

            if (DateTimeOffset.TryParse(initialGameData.prisonTimeEnd, out var prisonTime))
            {
                SetPrisonTimeEnd(prisonTime);
            }

            if (DateTimeOffset.TryParse(initialGameData.muteTimeEnd, out var muteTime))
            {
                SetMuteTimeEnd(muteTime);
            }

            onBeforeInitialized.OnNext(Unit.Default);
            isInitialized.Value = true;
        }

        public async UniTask<Result> AddXpAmount(int xpAmount, CancellationToken cancellationToken)
        {
            return await gameService.AddXpAmount(xpAmount, cancellationToken);
        }

        public async UniTask<Result> UpdateXpAmount(CancellationToken cancellationToken)
        {
            return await gameService.UpdateXpAmount(cancellationToken);
        }

        public void UpGameCount()
        {
            localStorage.GameCount++;
        }

        public bool IsPlayerModerator()
        {
            return UserLabelList.Contains(UserLabel.Moderator);
        }

        public bool IsPlayerModeratorTrainee()
        {
            return UserLabelList.Contains(UserLabel.TraineeMod);
        }

        public bool IsPlayerTester()
        {
            return UserLabelList.Contains(UserLabel.Tester);
        }

        public bool IsContentCreator1()
        {
            return UserLabelList.Contains(UserLabel.ContentCreator1);
        }

        public bool IsContentCreator2()
        {
            return UserLabelList.Contains(UserLabel.ContentCreator2);
        }

        public bool IsContentCreator3()
        {
            return UserLabelList.Contains(UserLabel.ContentCreator3);
        }

        public bool IsNormalPlayer()
        {
            return !IsPlayerModerator() && !IsPlayerModeratorTrainee() && !IsPlayerTester() && !IsContentCreator1() && !IsContentCreator2() && !IsContentCreator3();
        }

        public void SetPrisonTimeEnd(DateTimeOffset newPrisonTimeEnd)
        {
            SetPrisonTimeEndInternal(newPrisonTimeEnd);
        }

        public void SetMuteTimeEnd(DateTimeOffset newMuteTimeEnd)
        {
            SetMuteTimeEndInternal(newMuteTimeEnd);
        }

        public void ResetPrisonTimeEnd()
        {
            SetPrisonTimeEndInternal(DateTimeOffset.MinValue);
        }

        public void ResetMuteTimeEnd()
        {
            SetMuteTimeEndInternal(DateTimeOffset.MinValue);
        }

        public void UpdateGiftClaimTime()
        {
            GiftClaimTime = GameDateTime.Now;
            gameService.SetGiftClaimTime(GameDateTime.AsString(GiftClaimTime), DisposeCancellationToken);
        }

        private void SetPrisonTimeEndInternal(DateTimeOffset newPrisonTimeEnd)
        {
            if (prisonTimeEnd.Value == newPrisonTimeEnd)
            {
                return;
            }

            prisonTimeEnd.Value = newPrisonTimeEnd;
            gameService.SetPrisonEndTime(newPrisonTimeEnd.ToString("O"), DisposeCancellationToken);
        }

        private void SetMuteTimeEndInternal(DateTimeOffset newMuteTimeEnd)
        {
            if (muteTimeEnd.Value == newMuteTimeEnd)
            {
                return;
            }

            muteTimeEnd.Value = newMuteTimeEnd;
            gameService.SetMuteEndTime(newMuteTimeEnd.ToString("O"), DisposeCancellationToken);
        }
    }
}