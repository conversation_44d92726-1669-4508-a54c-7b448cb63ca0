using System;
using System.Collections.Generic;
using System.Reactive;
using System.Reactive.Subjects;
using Game.Core;
using Game.Core.Data;
using Game.Services;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VoxelPlay;

namespace Game.Models
{
    public class BlockInventoryModel : ModelBase
    {
        private readonly VoxelConfig voxelConfig;
        private readonly IBlockInventoryService blockInventoryService;
        private readonly List<BlockInventory> blockInventoryList = new(1000);
        private readonly ISubject<Unit> onUpdated = new Subject<Unit>();
        private readonly ISubject<Unit> onPurchased = new Subject<Unit>();

        public IObservable<Unit> OnUpdated => onUpdated;
        public IObservable<Unit> OnPurchased => onPurchased;

        public BlockInventoryModel(IBlockInventoryService blockInventoryService, VoxelConfig voxelConfig)
        {
            this.voxelConfig = voxelConfig;
            this.blockInventoryService = blockInventoryService;

            LoadBlockInventoryList();
        }

        public override void Dispose()
        {
            base.Dispose();
            blockInventoryList.Clear();
        }

        private void LoadBlockInventoryList()
        {
            blockInventoryList.Clear();
            blockInventoryList.AddRange(blockInventoryService.GetBlockInventoryList());
        }

        public void SaveBlockInventoryList()
        {
            blockInventoryService.SetBlockInventoryList(blockInventoryList);
        }

        public bool TryGiveAwayBlock(int blockId)
        {
            if (voxelConfig.IsInfiniteVoxelDef(blockId))
            {
                return false;
            }

            var blockInventory = blockInventoryList.Find(x => x.id == blockId);
            if (blockInventory is not { count: > 0 })
            {
                return false;
            }

            blockInventory.count = Mathf.Max(0, blockInventory.count - 1);
            onUpdated.OnNext(Unit.Default);
            return true;
        }

        public bool TryCollectBlock(VoxelDefinition voxelDef)
        {
            if (!voxelConfig.TryGetBlockData(voxelDef, out var blockData) || voxelConfig.IsInfiniteVoxelDef(blockData.id))
            {
                return false;
            }

            var blockInventory = blockInventoryList.Find(x => x.id == blockData.id);
            if (blockInventory == null)
            {
                blockInventory = new BlockInventory
                {
                    id = blockData.id
                };
                blockInventoryList.Add(blockInventory);
            }

            blockInventory.count += 1;
            onUpdated.OnNext(Unit.Default);
            return true;
        }

        public int GetBlockCount(int blockId)
        {
            if (voxelConfig.IsInfiniteVoxelDef(blockId))
            {
                return Constants.InfiniteBlockValue;
            }

            var blockInventory = blockInventoryList.Find(x => x.id == blockId);
            return blockInventory?.count ?? 0;
        }

        public void PurchaseBlockInventory(int id, int count)
        {
            var blockInventory = blockInventoryList.Find(x => x.id == id);
            if (blockInventory == null)
            {
                blockInventory = new BlockInventory
                {
                    id = id
                };
                blockInventoryList.Add(blockInventory);
            }

            blockInventory.count += count;
            onUpdated.OnNext(Unit.Default);
            onPurchased.OnNext(Unit.Default);
        }
    }
}