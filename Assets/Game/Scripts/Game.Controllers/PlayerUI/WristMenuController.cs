using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Modules.Core;
using VContainer;

namespace Game.Controllers.PlayerUI
{
    public class WristMenuController : ControllerBase
    {
        [Inject]
        private void Construct(EconomyModel economyModel, PlayersModel playersModel, LevelModel levelModel, PlayerMenu playerMenu)
        {
            var wristMenu = playerMenu.WristMenu;

            levelModel.OnLevelLoaded.Where(ok => ok)
                .Subscribe(_ => wristMenu.SetActivePlayerHp(levelModel.LevelConfig.UseAnyDamageOnPlayer))
                .AddTo(DisposeCancellationToken);
            economyModel.CoinAmount.Subscribe(c => wristMenu.SetCoinAmount(c)).AddTo(DisposeCancellationToken);
            economyModel.DiamondAmount.Subscribe(d => wristMenu.SetDiamondAmount(d)).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayer.Where(p => p).Subscribe(p =>
            {
                wristMenu.SetPlayerName(p.Name.Value);
                p.Health.Subscribe(hp => playerMenu.WristMenu.SetPlayerHp(hp)).AddTo(p);
            }).AddTo(DisposeCancellationToken);
        }
    }
}