using System;
using System.Collections.Generic;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Modules.Core;
using VContainer;

namespace Game.Controllers.PlayerUI
{
    public class HandsMenuController : ControllerBase
    {
        private HandsMenu handsMenu;
        private PlayersModel playersModel;
        private InteractablesMenu interactablesMenu;
        private EconomyModel economyModel;
        private LevelModel levelModel;

        [Inject]
        private void Construct(PlayersModel playersModel, PlayerMenu playerMenu, EconomyModel economyModel, LevelModel levelModel)
        {
            this.economyModel = economyModel;
            this.playersModel = playersModel;
            this.levelModel = levelModel;
            handsMenu = playerMenu.HandsMenu;
            interactablesMenu = playerMenu.InteractablesMenu;

            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            handsMenu.OnHomeClicked.Subscribe(HandleHomeClicked).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (levelModel.LevelConfig.DisableArmInteractableWidgets)
            {
                handsMenu.SetInteractableWidgetActive(false);
                handsMenu.SetInteractableWidgetNodeActive(false);
            }
            else
            {
                playersModel.ActiveInteractableList.Subscribe(HandleActiveInteractableList).AddTo(player);
                handsMenu.OnInteractablesMenuClicked.Subscribe(HandleInteractablesMenuClicked).AddTo(player);
                handsMenu.OnClicked.Subscribe(_ => OpenInteractablesMenu()).AddTo(player);
                handsMenu.SetInteractableWidgetActive(true);
                handsMenu.SetInteractableWidgetNodeActive(true);
            }
        }

        private void HandleActiveInteractableList(ActiveInteractableList activeInteractableList)
        {
            ValidateActiveInteractableList(activeInteractableList);
            handsMenu.SetActiveInteractableList(activeInteractableList);
        }

        private void ValidateActiveInteractableList(ActiveInteractableList activeInteractableList)
        {
            var itemsToRemove = new List<ActiveInteractable>();
            foreach (var activeInteractable in activeInteractableList)
            {
                var inventory = economyModel.InventoryList.Find(i => i.viewCode == activeInteractable.code);
                if (inventory == null || !inventory.IsOwned)
                {
                    itemsToRemove.Add(activeInteractable);
                }
            }

            itemsToRemove.ForEach(i => playersModel.RemoveActiveInteractable(i.hand, i.index));
        }

        private void HandleHomeClicked(Unit unit)
        {
            playersModel.RespawnLocalPlayer(new PlayerRespawnArgs(true));
        }

        private void HandleInteractablesMenuClicked(Unit unit)
        {
            OpenInteractablesMenu();
        }

        private void OpenInteractablesMenu()
        {
            if (interactablesMenu.IsMenuShowed.Value)
            {
                return;
            }

            interactablesMenu.ShowMenu(economyModel.GetOwnedInteractableList());
        }
    }
}