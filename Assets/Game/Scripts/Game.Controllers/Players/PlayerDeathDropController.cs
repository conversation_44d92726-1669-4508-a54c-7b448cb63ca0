using System;
using System.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Grabbing;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerDeathDropController : ControllerBase
    {
        private IXRInput xrInput;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRInput xrInput)
        {
            this.xrInput = xrInput;

            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            player.OnBeforeDeadLocal.Subscribe(_ => DropInteractables()).AddTo(player);
        }

        private void DropInteractables()
        {
            DropInteractables(xrInput.LeftDirectInteractor);
            DropInteractables(xrInput.RightDirectInteractor);
        }

        private void DropInteractables(XRDirectInteractor interactor)
        {
            if (!interactor.hasSelection)
            {
                return;
            }

            var interactables = interactor.interactablesSelected.ToList();
            foreach (var interactable in interactables)
            {
                if (interactable.transform.TryGetComponent<GrabbableItem>(out var grabbable))
                {
                    grabbable.Ungrab();
                }
            }
        }
    }
}