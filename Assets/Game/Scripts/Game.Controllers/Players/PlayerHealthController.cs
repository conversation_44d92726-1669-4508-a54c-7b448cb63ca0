using System;
using Cysharp.Threading.Tasks;
using Game.Views.Levels;
using Game.Views.Players;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerHealthController : ControllerBase
    {
        private LevelModel levelModel;

        [Inject]
        private void Construct(LevelModel levelModel, PlayersModel playersModel)
        {
            this.levelModel = levelModel;

            playersModel.OnLocalPlayerBeforeCreated.Subscribe(HandleLocalPlayerBeforeSpawned).AddTo(DisposeCancellationToken);
            playersModel.OnPlayerCreated.Subscribe(HandlePlayerCreated).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayerBeforeSpawned(PlayerActor player)
        {
            player.RestoreHealth();
        }

        private void HandlePlayerCreated(PlayerActor player)
        {
            player.SetActiveHealthSystem(levelModel.LevelConfig.UseAnyDamageOnPlayer);
        }
    }
}