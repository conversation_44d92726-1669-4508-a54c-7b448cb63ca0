using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerJoinedController : ControllerBase
    {
        private GameConfig gameConfig;
        private PlayersModel playersModel;

        [Inject]
        private void Construct(INetworkClient networkClient, PlayersModel playersModel, GameConfig gameConfig)
        {
            this.gameConfig = gameConfig;
            this.playersModel = playersModel;

            networkClient.OnPlayerJoined.Subscribe(x => HandlePlayerJoined(x.runner, x.player)).AddTo(DisposeCancellationToken);
        }

        public override void Initialize()
        {
            playersModel.CreateOfflineLocalPlayer();
        }

        private void HandlePlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            if (player != runner.LocalPlayer)
            {
                return;
            }

            if (runner.IsSharedModeMasterClient && player.PlayerId <= 1)
            {
                GameLogger.Player.Debug("Local player spawning MasterClientObject");
                runner.Spawn(gameConfig.MasterClientObjectPrefab, onBeforeSpawned: (_, _) => GameLogger.Player.Debug("MasterClientObject creator id: {0}", player.PlayerId));
            }

            playersModel.CreateLocalPlayer();
        }
    }
}