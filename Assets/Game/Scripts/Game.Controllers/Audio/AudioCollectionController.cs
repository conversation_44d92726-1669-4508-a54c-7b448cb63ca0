using Game.Core;
using Game.Views.Voxels;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Audio
{
    public class AudioCollectionController : ControllerBase
    {
        private GameConfig gameConfig;
        private IAudioClient audioClient;
        private AudioCollection voxelAudioCollection;

        [Inject]
        private void Construct(IAudioClient audioClient, GameConfig gameConfig, VoxelSpaceManager voxelSpaceManager)
        {
            this.gameConfig = gameConfig;
            this.audioClient = audioClient;
            voxelAudioCollection = voxelSpaceManager.GetVoxelAudioCollection();
        }

        public override void Initialize()
        {
            audioClient.AddAudioCollection(voxelAudioCollection);
            gameConfig.AudioCollectionList.ForEach(audioClient.AddAudioCollection);
        }

        public override void Dispose()
        {
            base.Dispose();
            audioClient.RemoveAudioCollection(voxelAudioCollection);
            gameConfig.AudioCollectionList.ForEach(audioClient.RemoveAudioCollection);
        }
    }
}