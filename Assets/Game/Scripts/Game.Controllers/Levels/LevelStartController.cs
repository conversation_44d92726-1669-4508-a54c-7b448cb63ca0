using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Photon.Realtime;
using Game.Controllers.States;
using Game.Core;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Network;
using Game.Views.Players;
using Game.Views.UI.Screens.Loading;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using Modules.Oculus;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Levels
{
    public class LevelStartController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private GameModel gameModel;
        private AppConfig appConfig;
        private LevelModel levelModel;
        private VoxelModel voxelModel;
        private GameConfig gameConfig;
        private StateMachine stateMachine;
        private PlayersModel playersModel;
        private IOculusClient oculusClient;
        private LoadingScreen loadingScreen;
        private INetworkClient networkClient;
        private VoxelSpaceManager voxelSpaceManager;

        private bool isLevelStarting;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            INetworkClient networkClient,
            GameModel gameModel,
            StateMachine stateMachine,
            IXRPlayer xrPlayer,
            VoxelSpaceManager voxelSpaceManager,
            VoxelModel voxelModel,
            PlayersModel playersModel,
            AppConfig appConfig,
            GameConfig gameConfig,
            IOculusClient oculusClient,
            IScreenManager screenManager)
        {
            this.xrPlayer = xrPlayer;
            this.gameModel = gameModel;
            this.appConfig = appConfig;
            this.gameConfig = gameConfig;
            this.levelModel = levelModel;
            this.voxelModel = voxelModel;
            this.playersModel = playersModel;
            this.stateMachine = stateMachine;
            this.oculusClient = oculusClient;
            this.networkClient = networkClient;
            this.voxelSpaceManager = voxelSpaceManager;
            loadingScreen = screenManager.GetScreen<LoadingScreen>();

            levelModel.OnLevelStarting.Subscribe(args => StartLevel(args).Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid StartLevel(LoadLevelArgs args)
        {
            if (isLevelStarting)
            {
                GameLogger.Level.Debug("Level already started");
                return;
            }

            GameLogger.Level.Debug("Level starting: {0}", args.levelId);
            isLevelStarting = true;

            DisableMapGeneration();
            SetActiveLocomotions(false);
            await Disconnect();
            loadingScreen.Show();
            TeleportPlayerToSafeZone();

            if (!await TryLoadLevel(args))
            {
                return;
            }

            await InitializeMap();
            TeleportPlayerToLevel().Forget();

            if (!await TryConnect())
            {
                return;
            }

            SetSliceMap().Forget();
            SetActiveLocomotions(true);
            FadeOut();

            GameLogger.Level.Debug("Level started: {0}", args.levelId);
            isLevelStarting = false;
            loadingScreen.Hide();
        }

        private void SetActiveLocomotions(bool isActive)
        {
            playersModel.SetActiveLocomotions(isActive);
        }

        private async UniTask Disconnect()
        {
            await UniTask.WhenAll(networkClient.Disconnect(cancellationToken: DisposeCancellationToken), xrPlayer.FadeInViewAsync());
        }

        private void TeleportPlayerToSafeZone()
        {
            var pose = new Pose(1000 * Vector3.up, Quaternion.identity);
            playersModel.TeleportLocalPlayer(new PlayerTeleportArgs(pose, false));
        }

        private async UniTaskVoid TeleportPlayerToLevel()
        {
            await UniTask.WaitUntil(() => voxelSpaceManager.IsMapLoaded.Value, cancellationToken: DisposeCancellationToken);
            playersModel.RespawnLocalPlayer(new PlayerRespawnArgs(false));
        }

        private void FadeOut()
        {
            xrPlayer.FadeOutView();
        }

        private void DisableMapGeneration()
        {
            voxelSpaceManager.SetMapGenerationEnabled(false);
        }

        private async UniTask<bool> TryLoadLevel(LoadLevelArgs args)
        {
            var request = await levelModel.LoadLevel(args.levelId, args.password, DisposeCancellationToken);
            if (request.IsFail)
            {
                isLevelStarting = false;
                stateMachine.SetState<RestartState>();
                return false;
            }

            return true;
        }

        private async UniTask InitializeMap()
        {
            var map = levelModel.IsLobbyLevel || levelModel.IsMinesLevel ? null : await levelModel.LoadMap(DisposeCancellationToken);
            voxelSpaceManager.InitializeMap(levelModel.Level.worldTemplateId, levelModel.Level.worldExtents, map);
        }

        private async UniTask<bool> TryConnect()
        {
            var authenticationValues = await GetAuthenticationValues(DisposeCancellationToken);
            var args = new ConnectSessionArgs(gameConfig.MaxPlayerCount, MatchmakingMode.RandomMatching, authenticationValues, GetSessionProperties());
            var result = await networkClient.ConnectSession(args, DisposeCancellationToken);
            if (!result.Ok)
            {
                isLevelStarting = false;
                stateMachine.SetState<RestartState>();
                return false;
            }

            return true;
        }

        private async UniTaskVoid SetSliceMap()
        {
            var sliceMap = await levelModel.LoadSliceMap(networkClient.SessionInfo.Name, networkClient.DisconnectionCancellationToken);
            await UniTask.WaitUntil(() => voxelSpaceManager.IsMapLoaded.Value, cancellationToken: networkClient.DisconnectionCancellationToken);
            voxelModel.SetSliceMap(sliceMap);
        }

        private async UniTask<string> GetNonce(CancellationToken cancellationToken)
        {
            Result<string> result;

            do
            {
                result = await oculusClient.GetNonce(cancellationToken);

                if (result.IsFail)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken: cancellationToken);
                }
            } while (result.IsFail);

            return result.Value;
        }

        private async UniTask<AuthenticationValues> GetAuthenticationValues(CancellationToken cancellationToken)
        {
            if (appConfig.ApiType == ApiType.Dev || !gameConfig.NetworkAuthEnabled)
            {
                return null;
            }

            var nonce = await GetNonce(cancellationToken);
            var authentication = new AuthenticationValues
            {
                AuthType = CustomAuthenticationType.Oculus
            };
            authentication.AddAuthParameter("userid", oculusClient.UserId);
            authentication.AddAuthParameter("nonce", nonce);

            return authentication;
        }

        private Dictionary<string, SessionProperty> GetSessionProperties()
        {
            var properties = new Dictionary<string, SessionProperty>
            {
                [SessionPropertyKeys.LevelId] = levelModel.Level.id,
                [SessionPropertyKeys.LobbyName] = gameModel.LobbyName,
                [SessionPropertyKeys.IsClose] = false
            };

            if (!levelModel.Level.isPublished)
            {
                properties.Add(SessionPropertyKeys.IsPrivate, true);
            }

            return properties;
        }
    }
}