using System;
using Cysharp.Threading.Tasks;
using Game.Views.Interactables;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;
using Random = UnityEngine.Random;

namespace Game.Controllers.Voxels
{
    public class VoxelOreSpawnController : ControllerBase
    {
        private VoxelConfig voxelConfig;
        private InteractablesManager interactablesManager;

        [Inject]
        private void Construct(VoxelSpaceManager voxelSpaceManager, VoxelConfig voxelConfig, InteractablesManager interactablesManager)
        {
            this.voxelConfig = voxelConfig;
            this.interactablesManager = interactablesManager;

            voxelSpaceManager.OnVoxelDestroyed.Subscribe(HandleVoxelDestroyed).AddTo(DisposeCancellationToken);
        }

        private void HandleVoxelDestroyed(DestroyedVoxelArgs args)
        {
            if (args.byLocalPlayer && voxelConfig.TryGetBlockData(args.voxelDefinition, out var voxelData))
            {
                var viewCode = voxelData.voxel.name;
                var pose = new Pose(args.position, Random.rotation);
                interactablesManager.CreateActor(viewCode, pose);
            }
        }
    }
}