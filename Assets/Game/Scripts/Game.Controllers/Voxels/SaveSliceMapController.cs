using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class SaveSliceMapController : ControllerBase
    {
        private VoxelModel voxelModel;
        private GameConfig gameConfig;
        private LevelModel levelModel;
        private VoxelSpaceManager voxelSpaceManager;
        private INetworkClient networkClient;
        private CancellationTokenSource saveCancellationTokenSource;

        private bool IsMasterClient => networkClient.IsMasterClient.Value;
        private bool IsConnected => networkClient.IsConnected.Value;
        private bool IsVoxelWorldLoaded => voxelSpaceManager.IsMapLoaded.Value;
        private bool IsReadyToSave => IsVoxelWorldLoaded && IsConnected && IsMasterClient;

        [Inject]
        private void Construct(LevelModel levelModel, VoxelSpaceManager voxelSpaceManager, VoxelModel voxelModel, INetworkClient networkClient, GameConfig gameConfig)
        {
            this.voxelModel = voxelModel;
            this.gameConfig = gameConfig;
            this.levelModel = levelModel;
            this.voxelSpaceManager = voxelSpaceManager;
            this.networkClient = networkClient;

            voxelSpaceManager.IsMapLoaded.Subscribe(_ => StartSaveSliceMapTimer()).AddTo(DisposeCancellationToken);
            networkClient.IsConnected.Subscribe(_ => StartSaveSliceMapTimer()).AddTo(DisposeCancellationToken);
            networkClient.IsMasterClient.Subscribe(_ => StartSaveSliceMapTimer()).AddTo(DisposeCancellationToken);
            voxelModel.OnSliceVoxelSaving.Subscribe(_ => SaveSliceMap().Forget()).AddTo(DisposeCancellationToken);
        }

        private void StartSaveSliceMapTimer()
        {
            saveCancellationTokenSource.CancelAndDispose();

            if (!IsReadyToSave)
            {
                return;
            }

            saveCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(networkClient.DisconnectionCancellationToken, DisposeCancellationToken);
            UniTaskAsyncEnumerable
                .Interval(TimeSpan.FromSeconds(gameConfig.SliceVoxelsInterval)).Subscribe(_ => SaveSliceMap().Forget())
                .AddTo(saveCancellationTokenSource.Token);
        }

        private async UniTaskVoid SaveSliceMap()
        {
            if (!IsReadyToSave)
            {
                return;
            }

            var sliceMap = voxelModel.GetSliceMapBinary();
            if (sliceMap == null)
            {
                return;
            }

            var ok = await levelModel.SaveSliceMap(networkClient.SessionInfo.Name, sliceMap);
            if (ok && IsConnected)
            {
                var time = Mathf.Max(0, Mathf.CeilToInt(networkClient.ServerTime - (gameConfig.SliceVoxelsInterval + 1)));
                voxelSpaceManager.SetSliceVoxelsServerTime(time);
            }

            StartSaveSliceMapTimer();
        }
    }
}