using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Models;
using Game.Views.BuildIslands;
using Game.Views.Levels;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;
using VoxelPlay;

namespace Game.Controllers.Voxels
{
    public class DamageVoxelController : ControllerBase
    {
        private LevelModel levelModel;
        private BuildZoneHandler buildZoneHandler;
        private VoxelSpaceManager voxelSpaceManager;
        private BlockInventoryModel blockInventoryModel;

        private void SetDamage(VoxelRadiusDamageArgs args)
        {
            var result = new List<VoxelIndex>();
            if (voxelSpaceManager.DamageVoxel(args.point, args.damage, args.radius, result))
            {
                foreach (var voxel in result)
                {
                    if (!buildZoneHandler.IsValidPoint(voxel.position))
                    {
                        continue;
                    }

                    SetDamage(voxel.chunk, voxel.voxelIndex, voxel.position, args.damage, voxel.type);
                }
            }
        }

        [Inject]
        private void Construct(
            VoxelModel voxelModel,
            LevelModel levelModel,
            BuildZoneHandler buildZoneHandler,
            VoxelSpaceManager voxelSpaceManager,
            BlockInventoryModel blockInventoryModel)
        {
            this.levelModel = levelModel;
            this.buildZoneHandler = buildZoneHandler;
            this.voxelSpaceManager = voxelSpaceManager;
            this.blockInventoryModel = blockInventoryModel;

            voxelModel.OnPointDamage.Subscribe(SetDamage).AddTo(DisposeCancellationToken);
            voxelModel.OnRadiusDamage.Subscribe(SetDamage).AddTo(DisposeCancellationToken);
        }

        private void SetDamage(VoxelPointDamageArgs args)
        {
            var origin = args.point + 0.1f * args.normal;

            if (!buildZoneHandler.IsValidPoint(args.point))
            {
                return;
            }

            if (voxelSpaceManager.RaycastVoxel(origin, -args.normal, 0.2f, out var hitInfo))
            {
                SetDamage(hitInfo.chunk, hitInfo.voxelIndex, hitInfo.voxelCenter, args.damage, hitInfo.voxel.type);
            }
        }

        private void SetDamage(VoxelChunk chunk, int index, Vector3 position, int damage, VoxelDefinition voxelDef)
        {
            var placeholder = voxelSpaceManager.GetVoxelPlaceholder(chunk, index);
            if (placeholder.resistancePointsLeft == 255)
            {
                return;
            }

            if (placeholder.resistancePointsLeft > damage)
            {
                voxelSpaceManager.DamageVoxel(position, damage);
            }
            else
            {
                voxelSpaceManager.DestroyVoxelNetworked(position);
                if (levelModel.Level.useBlockInventory)
                {
                    blockInventoryModel.TryCollectBlock(voxelDef);
                }
            }
        }
    }
}