using System;
using System.Collections.Generic;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Mirror;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Mirror
{
    public class MirrorController : ControllerBase
    {
        private LevelModel levelModel;
        private MirrorManager mirrorManager;
        private LobbySpaceManager lobbySpaceManager;
        private LevelSpaceManager levelSpaceManager;

        [Inject]
        private void Construct(MirrorManager mirrorManager, LobbySpaceManager lobbySpaceManager, LevelSpaceManager levelSpaceManager, LevelModel levelModel)
        {
            this.levelSpaceManager = levelSpaceManager;
            this.lobbySpaceManager = lobbySpaceManager;
            this.levelModel = levelModel;
            this.mirrorManager = mirrorManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            mirrorManager.DestroyAll();

            var mirrorPoses = new List<Pose>();
            if (levelModel.IsLobbyLevel)
            {
                mirrorPoses.AddRange(lobbySpaceManager.MirrorNode.GetChildPoses());
            }
            else if (levelModel.IsMinesLevel)
            {
                mirrorPoses.AddRange(levelSpaceManager.MirrorNode.GetChildPoses());
            }

            mirrorPoses.ForEach(p => mirrorManager.Create(p));
        }
    }
}