using System;
using System.Reactive;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.PlayerUI.Backpack;
using Modules.Core;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Backpack
{
    public class BackpackController : ControllerBase
    {
        private BackpackMenu backpackMenu;
        private PlayersModel playersModel;
        private CancellationTokenSource equipCancellationTokenSource;
        private IInteractableInteractionSubscriber interactableInteractionsSubscriber;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool IsBackpackEquipped => LocalPlayer != null && LocalPlayer.Backpack != null;

        [Inject]
        private void Construct(PlayerMenu playerMenu, PlayersModel playersModel, IInteractableInteractionSubscriber interactableInteractionsSubscriber)
        {
            this.playersModel = playersModel;
            backpackMenu = playerMenu.BackpackMenu;
            this.interactableInteractionsSubscriber = interactableInteractionsSubscriber;

            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            backpackMenu.OnUnequip.Subscribe(UnequipBackpack).AddTo(player);
            player.OnBeforeDeadLocal.Subscribe(HandleLocalPlayerBeforeDead).AddTo(player);
            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleExitEntered).AddTo(player);
            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(player);
        }

        private void HandleLocalPlayerBeforeDead(Unit unit)
        {
            LocalPlayer.UnequipBackpack();
            backpackMenu.DeactivateMenu();
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (IsBackpackEquipped || args.interactable is not BackpackActor backpack)
            {
                return;
            }

            backpackMenu.RenderActivatingState(backpack.InteractableCode);
        }

        private void HandleExitEntered(InteractionArgs<SelectExitEventArgs> args)
        {
            if (IsBackpackEquipped || args.interactable is not BackpackActor backpack)
            {
                return;
            }

            if (backpackMenu.IsHoveredBackpack(backpack))
            {
                EquipBackpack(backpack);
            }
            else
            {
                backpackMenu.DeactivateMenu();
            }
        }

        private void EquipBackpack(BackpackActor backpack)
        {
            LocalPlayer.EquipBackpack(backpack);
            backpackMenu.RenderActivatedState(backpack.InteractableCode);
        }

        private void UnequipBackpack(IXRSelectInteractor interactor)
        {
            if (LocalPlayer.Backpack == null)
            {
                return;
            }

            var backpack = LocalPlayer.Backpack;
            LocalPlayer.UnequipBackpack();
            backpack.Grab(interactor);
        }
    }
}