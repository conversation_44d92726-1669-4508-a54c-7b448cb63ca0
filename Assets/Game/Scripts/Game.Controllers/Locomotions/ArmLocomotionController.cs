using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.View.Locomotions;
using Game.Views.Players;
using Game.Views.Voxels;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Locomotions
{
    public class ArmLocomotionController : ControllerBase
    {
        private IXRInput xrInput;
        private IAudioClient audioClient;
        private VoxelSpaceManager voxelSpaceManager;
        private VoxelConfig voxelConfig;
        private ArmLocomotion armLocomotion;

        [Inject]
        private void Construct(LocomotionSystem locomotionSystem, PlayersModel playersModel, IAudioClient audioClient, VoxelSpaceManager voxelSpaceManager, VoxelConfig voxelConfig, IXRInput xrInput)
        {
            this.xrInput = xrInput;
            this.voxelConfig = voxelConfig;
            this.audioClient = audioClient;
            this.voxelSpaceManager = voxelSpaceManager;

            locomotionSystem.TryGetLocomotion(out armLocomotion);
            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayerScale.Subscribe(armLocomotion.SetScale).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            armLocomotion.OnTouched.Subscribe(HandleHandTouched).AddTo(player);
        }

        private void HandleHandTouched(ActiveArm activeArm)
        {
            if (!Application.isFocused)
            {
                return;
            }

            var contact = activeArm.contact;
            var point = contact.point - 0.1f * contact.normal;
            var ok = voxelSpaceManager.TryPlayCollisionSound(point);

            if (!ok)
            {
                audioClient.PlayOneInstance(voxelConfig.VoxelTouchAudioKey, point, DisposeCancellationToken);
            }

            xrInput.SendHapticImpulse(activeArm.hand, 0.05f, 0.05f);
        }
    }
}