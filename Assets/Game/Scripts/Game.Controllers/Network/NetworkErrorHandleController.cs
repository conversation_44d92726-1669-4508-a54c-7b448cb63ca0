using System;
using Cysharp.Threading.Tasks;
using Game.Services;
using Game.Views.Levels;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Network
{
    public class NetworkErrorHandleController : ControllerBase
    {
        private const string BufferShouldBeNullKey = "Buffer should be null";

        private LevelModel levelModel;
        private INetworkClient networkClient;
        private IAnalyticsService analyticsService;

        private bool errorReceived;

        [Inject]
        private void Construct(LevelModel levelModel, INetworkClient networkClient, IAnalyticsService analyticsService)
        {
            this.levelModel = levelModel;
            this.networkClient = networkClient;
            this.analyticsService = analyticsService;

            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(DisposeCancellationToken);
            Application.logMessageReceived += HandleLogReceived;
        }

        public override void Dispose()
        {
            base.Dispose();
            Application.logMessageReceived -= HandleLogReceived;
        }

        private void HandleLogReceived(string condition, string stacktrace, LogType logType)
        {
            if (!errorReceived && networkClient.IsConnected.Value && logType == LogType.Exception && condition.Contains(BufferShouldBeNullKey))
            {
                errorReceived = true;

                var levelName = levelModel.Level.name;
                var playerId = networkClient.LocalPlayer.PlayerId;
                var localtime = Mathf.RoundToInt(networkClient.LocalTime);
                var serverTime = Mathf.RoundToInt(networkClient.ServerTime);
                analyticsService.BufferShouldBeNullError(levelName, playerId, localtime, serverTime);
            }
        }

        private void HandleShutdown()
        {
            errorReceived = false;
        }
    }
}