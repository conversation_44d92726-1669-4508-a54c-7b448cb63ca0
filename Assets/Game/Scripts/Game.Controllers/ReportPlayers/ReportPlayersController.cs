using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.Models;
using Game.Views.Levels;
using Game.Views.Moderation;
using Game.Views.Players;
using Game.Views.UI.Screens.Menu;
using Game.Views.UI.Screens.ReportPlayers;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using VContainer;

namespace Game.Controllers.ReportPlayers
{
    public class ReportPlayersController : ControllerBase
    {
        private GameModel gameModel;
        private MenuScreen menuScreen;
        private PlayersModel playersModel;
        private ModerationModel moderationModel;
        private ReportPlayersModel reportPlayersModel;
        private ReportPlayersScreen reportPlayersScreen;
        private ModerationSentencePanel moderationSentencePanel;
        private CancellationTokenSource reportFlowCancellationTokenSource;

        [Inject]
        private void Construct(
            GameModel gameModel,
            LevelModel levelModel,
            PlayersModel playersModel,
            IScreenManager screenManager,
            INetworkClient networkClient,
            ModerationModel moderationModel,
            ReportPlayersModel reportPlayersModel)
        {
            this.gameModel = gameModel;
            this.playersModel = playersModel;
            this.moderationModel = moderationModel;
            this.reportPlayersModel = reportPlayersModel;
            menuScreen = screenManager.GetScreen<MenuScreen>();
            moderationSentencePanel = menuScreen.ModerationSentencePanel;
            reportPlayersScreen = screenManager.GetScreen<ReportPlayersScreen>();

            levelModel.OnLevelLoaded.Subscribe(HandleLevelLoaded).AddTo(DisposeCancellationToken);
            networkClient.OnShutdown.Subscribe(_ => ResetState()).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            reportFlowCancellationTokenSource.CancelAndDispose();
        }

        private void ResetState()
        {
            reportPlayersModel.Clear();
            reportFlowCancellationTokenSource.CancelAndDispose();
        }

        private void HandleLevelLoaded(bool ok)
        {
            ResetState();

            if (!ok)
            {
                return;
            }

            reportFlowCancellationTokenSource = new CancellationTokenSource();
            playersModel.OnPlayerCreated.Subscribe(HandlePlayerCreated).AddTo(reportFlowCancellationTokenSource.Token);
            playersModel.OnPlayerDestroyed.Subscribe(HandlePlayerDestroyed).AddTo(reportFlowCancellationTokenSource.Token);
            reportPlayersModel.OnReportPlayersUpdated.Subscribe(HandleReportPlayersUpdated).AddTo(reportFlowCancellationTokenSource.Token);
            reportPlayersScreen.OnMuteClicked.Subscribe(HandleMuteClicked).AddTo(reportFlowCancellationTokenSource.Token);
            reportPlayersScreen.OnReportClicked.Subscribe(widget => HandleReportClicked(widget).Forget()).AddTo(reportFlowCancellationTokenSource.Token);
        }

        private void HandlePlayerCreated(PlayerActor player)
        {
            if (player.HasStateAuthority)
            {
                return;
            }

            reportPlayersModel.AddPlayer(player);
        }

        private void HandlePlayerDestroyed(PlayerActor player)
        {
            reportPlayersModel.RemovePlayer(player.StateAuthority.PlayerId);
        }

        private void HandleReportPlayersUpdated(List<ReportPlayerData> reportPlayers)
        {
            reportPlayersScreen.Render(reportPlayers);
        }

        private void HandleMuteClicked(ReportPlayerWidget widget)
        {
            if (!playersModel.TryGetPlayer(widget.WidgetData.playerId, out var player))
            {
                return;
            }

            player.MuteVoiceLocal(!player.IsMuteLocal);

            var isMuted = player.IsMuteLocal || widget.WidgetData.isReported.Value;
            widget.WidgetData.isMuted.Value = isMuted;
        }

        private async UniTaskVoid HandleReportClicked(ReportPlayerWidget widget)
        {
            if (!playersModel.TryGetPlayer(widget.WidgetData.playerId, out var player))
            {
                return;
            }

            if (gameModel.IsPlayerModerator() || gameModel.IsPlayerModeratorTrainee())
            {
                menuScreen.Show();
                menuScreen.OpenPanel<LoadingPanel>();
                await UniTask.WaitForSeconds(0.5f, cancellationToken: reportFlowCancellationTokenSource.Token);
                moderationSentencePanel.Render(widget.WidgetData, moderationModel.AllowedSentences);
                menuScreen.OpenPanel<ModerationSentencePanel>();
            }
            else
            {
                player.MuteVoiceLocal(true);
                if (!widget.WidgetData.isReported.Value)
                {
                    widget.WidgetData.isReported.Value = true;
                }
            }
        }
    }
}