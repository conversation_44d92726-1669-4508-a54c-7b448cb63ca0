using System;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Models;
using Game.Views.Interactables;
using Game.Views.PlayerUI;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Interactables
{
    public class CreateInteractableController : ControllerBase
    {
        private IAudioClient audioClient;
        private IScreenManager screenManager;
        private InteractablesModel interactablesModel;
        private InteractablesManager interactablesManager;

        [Inject]
        private void Construct(
            PlayerMenu playerMenu,
            IAudioClient audioClient,
            IScreenManager screenManager,
            InteractablesModel interactablesModel,
            InteractablesManager interactablesManager)
        {
            this.audioClient = audioClient;
            this.screenManager = screenManager;
            this.interactablesModel = interactablesModel;
            this.interactablesManager = interactablesManager;

            playerMenu.HandsMenu.OnGrabbed.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            playerMenu.HandsMenu.OnMoneyBagSpawned.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            playerMenu.InteractablesMenu.OnGrabbed.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            interactablesModel.OnInteractableCreating.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
        }

        private void HandleCreateInteractable(InteractableWidget widget)
        {
            HandleCreateInteractable(widget.InteractableCode, widget.GrabInteractor, false);
        }

        private void HandleCreateInteractable(CreateInteractableArgs args)
        {
            HandleCreateInteractable(args.code, args.interactor, args.playSound);
        }

        private void HandleCreateInteractable(string code, IXRSelectInteractor interactor, bool playSound)
        {
            if (interactor is not { hasSelection: true })
            {
                return;
            }

            if (!interactablesModel.CanCreateNewInteractable)
            {
                var notificationScreen = screenManager.GetScreen<NotificationScreen>();
                notificationScreen.Show("You have exceeded the maximum number of items", 5);
                return;
            }

            var interactable = interactablesManager.CreateActor(code);
            if (interactable != null)
            {
                if (playSound)
                {
                    audioClient.Play(AudioKeys.Goal, Vector3.zero, DisposeCancellationToken);
                }

                interactable.Grab(interactor);
            }
        }
    }
}