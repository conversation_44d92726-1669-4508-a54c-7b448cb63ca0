using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Levels;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Interactables
{
    public class InteractableDropTimeoutController : ControllerBase
    {
        private LevelModel levelModel;
        private GameConfig gameConfig;
        private SharedInteractableSettings sharedInteractableSettings;

        [Inject]
        private void Construct(LevelModel levelModel, GameConfig gameConfig, SharedInteractableSettings sharedInteractableSettings)
        {
            this.gameConfig = gameConfig;
            this.levelModel = levelModel;
            this.sharedInteractableSettings = sharedInteractableSettings;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            sharedInteractableSettings.DropTimeout = levelModel.IsMinesLevel ? gameConfig.MinesDropTimeout : gameConfig.DefaultDropTimeout;
            sharedInteractableSettings.MaxInteractablesPerPlayer = levelModel.IsMinesLevel ? gameConfig.MinesMaxInteractablesPerPlayer : gameConfig.DefaultMaxInteractablesPerPlayer;
        }
    }
}