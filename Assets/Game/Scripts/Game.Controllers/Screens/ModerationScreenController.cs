using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core.Data;
using Game.Models;
using Game.Views.Moderation;
using Game.Views.Players;
using Game.Views.UI.Screens.Menu;
using Game.Views.UI.Screens.ReportPlayers;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.ReportPlayers
{
    public class ModerationScreenController : ControllerBase
    {
        private GameModel gameModel;
        private MenuScreen menuScreen;
        private PlayersModel playersModel;
        private ModerationModel moderationModel;
        private ModerationPanel moderationPanel;
        private ReportPlayersModel reportPlayersModel;
        private ModerationSentencePanel moderationSentencePanel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            GameModel gameModel,
            PlayersModel playersModel,
            IScreenManager screenManager,
            ModerationModel moderationModel,
            ReportPlayersModel reportPlayersModel)
        {
            this.gameModel = gameModel;
            this.playersModel = playersModel;
            this.moderationModel = moderationModel;
            this.reportPlayersModel = reportPlayersModel;
            menuScreen = screenManager.GetScreen<MenuScreen>();
            moderationPanel = menuScreen.ModerationPanel;
            moderationSentencePanel = menuScreen.ModerationSentencePanel;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            reportPlayersModel.OnReportPlayersUpdated.Subscribe(HandleReportPlayersUpdated).AddTo(player);
            moderationPanel.OnMuteClicked.Subscribe(HandleMuteClicked).AddTo(player);
            moderationPanel.OnReportClicked.Subscribe(widget => HandleReportClicked(widget).Forget()).AddTo(player);
        }

        private void HandleReportPlayersUpdated(List<ReportPlayerData> reportPlayers)
        {
            moderationPanel.Render(reportPlayers);
        }

        private void HandleMuteClicked(ReportPlayerWidget widget)
        {
            if (!playersModel.TryGetPlayer(widget.WidgetData.playerId, out var player))
            {
                return;
            }

            player.MuteVoiceLocal(!player.IsMuteLocal);

            var isMuted = player.IsMuteLocal || widget.WidgetData.isReported.Value;
            widget.WidgetData.isMuted.Value = isMuted;
        }

        private async UniTaskVoid HandleReportClicked(ReportPlayerWidget widget)
        {
            if (!playersModel.TryGetPlayer(widget.WidgetData.playerId, out var player))
            {
                return;
            }

            if (gameModel.IsPlayerModerator() || gameModel.IsPlayerModeratorTrainee())
            {
                menuScreen.Show();
                menuScreen.OpenPanel<LoadingPanel>();
                await UniTask.WaitForSeconds(0.5f, cancellationToken: LocalPlayer.destroyCancellationToken);
                moderationSentencePanel.Render(widget.WidgetData, moderationModel.AllowedSentences);
                menuScreen.OpenPanel<ModerationSentencePanel>();
            }
            else
            {
                player.MuteVoiceLocal(true);
                if (!widget.WidgetData.isReported.Value)
                {
                    widget.WidgetData.isReported.Value = true;
                }
            }
        }
    }
}