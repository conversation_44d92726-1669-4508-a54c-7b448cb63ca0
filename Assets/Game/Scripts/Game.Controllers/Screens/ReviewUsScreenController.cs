using System;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Views.UI.Screens.ReviewUs;
using Modules.Core;
using Modules.UI;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Screens
{
    public class ReviewUsScreenController : ControllerBase
    {
        [Inject]
        private void Construct(IScreenManager screenManager)
        {
            var announcementScreen = screenManager.GetScreen<ReviewUsScreen>();
            announcementScreen.OnReviewUsButtonClicked.Subscribe(_ => HandleReviewUsButtonClicked()).AddTo(DisposeCancellationToken);
        }

        private void HandleReviewUsButtonClicked()
        {
            Application.OpenURL(Constants.GameLink);
        }
    }
}