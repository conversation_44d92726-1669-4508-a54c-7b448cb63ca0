using Game.Views.UI.Screens.Announcement;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class AnnouncementScreenController : ControllerBase
    {
        [Inject]
        private void Construct(IScreenManager screenManager, AppConfig appConfig)
        {
            var announcementScreen = screenManager.GetScreen<AnnouncementScreen>();
            announcementScreen.SetGameVersion(appConfig.GameVersion);
        }
    }
}