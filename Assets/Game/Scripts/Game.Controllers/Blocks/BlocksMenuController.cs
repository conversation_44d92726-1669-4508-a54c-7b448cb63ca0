using System;
using System.Collections.Generic;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Levels;
using Game.Views.PlayerUI;
using Game.Views.PlayerUI.Blocks;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Blocks
{
    public class BlocksMenuController : ControllerBase
    {
        private VoxelModel voxelModel;
        private BlocksMenu blocksMenu;
        private LevelModel levelModel;
        private VoxelConfig voxelConfig;
        private BlockInventoryModel blockInventoryModel;
        private readonly List<BlockToolActor> blockToolList = new();

        [Inject]
        private void Construct(
            PlayerMenu playerMenu,
            VoxelModel voxelModel,
            VoxelConfig voxelConfig,
            LevelModel levelModel,
            INetworkClient networkClient,
            BlockInventoryModel blockInventoryModel,
            IInteractableInteractionSubscriber interactableInteractionsSubscriber)
        {
            this.levelModel = levelModel;
            this.voxelModel = voxelModel;
            this.voxelConfig = voxelConfig;
            blocksMenu = playerMenu.BlocksMenu;
            this.blockInventoryModel = blockInventoryModel;

            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(DisposeCancellationToken);
            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleExitEntered).AddTo(DisposeCancellationToken);

            blocksMenu.OnClicked.Subscribe(HandleBlockWidgetClicked).AddTo(DisposeCancellationToken);
            blocksMenu.OnGrabbed.Subscribe(HandleBlockWidgetClicked).AddTo(DisposeCancellationToken);

            blockInventoryModel.OnUpdated.Subscribe(HandleBlockInventoryUpdated).AddTo(DisposeCancellationToken);

            networkClient.OnShutdown.Subscribe(_ => blockToolList.Clear()).AddTo(DisposeCancellationToken);
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (args.interactable is not BlockToolActor blockTool)
            {
                return;
            }

            voxelModel.SelectedVoxelId.Subscribe(id => HandleSelectedBlockId(blockTool, id)).AddTo(blockTool.DropCancellationToken);
            blockTool.OnBlockClicked.Subscribe(HandleBlockToolClicked).AddTo(blockTool.DropCancellationToken);
            blockTool.OnFired.Subscribe(HandleBlockToolFired).AddTo(blockTool.DropCancellationToken);
            blockToolList.Add(blockTool);
        }

        private void HandleExitEntered(InteractionArgs<SelectExitEventArgs> args)
        {
            if (args.interactable is not BlockToolActor blockTool)
            {
                return;
            }

            blockToolList.Remove(blockTool);
            blockTool.ClearBlock();
            blocksMenu.HideMenu();
        }

        private void HandleSelectedBlockId(BlockToolActor blockTool, int blockId)
        {
            var blockWidgetData = new BlockWidgetData
            {
                id = blockId,
                count = blockInventoryModel.GetBlockCount(blockId)
            };
            blockTool.SetBlock(blockWidgetData, levelModel.Level.useBlockInventory);
        }

        private void HandleBlockToolClicked(BlockToolActor blockTool)
        {
            var blockWidgetDataList = new List<BlockWidgetData>();
            foreach (var blockData in voxelConfig.PlayerBlockDataList)
            {
                if (blockData.IsInfinite)
                {
                    blockWidgetDataList.Add(new BlockWidgetData
                    {
                        id = blockData.id,
                        count = Constants.InfiniteBlockValue
                    });
                }
                else
                {
                    var count = blockInventoryModel.GetBlockCount(blockData.id);
                    blockWidgetDataList.Add(new BlockWidgetData
                    {
                        id = blockData.id,
                        count = count
                    });
                }
            }

            blocksMenu.ShowMenu(blockWidgetDataList, levelModel.Level.useBlockInventory);
        }

        private void HandleBlockToolFired(BlockToolActor blockTool)
        {
            blocksMenu.HideMenu();
        }

        private void HandleBlockWidgetClicked(BlockWidget widget)
        {
            voxelModel.SetSelectedVoxelId(widget.Id);
            blocksMenu.HideMenu();
        }

        private void HandleBlockInventoryUpdated(Unit unit)
        {
            foreach (var blockTool in blockToolList)
            {
                blockTool.SetBlockCount(blockInventoryModel.GetBlockCount(blockTool.BlockId));
            }
        }
    }
}