using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Services;
using Game.Views.Avatars;
using Game.Views.Levels;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Analytics
{
    public class AnalyticsController : ControllerBase
    {
        private GameModel gameModel;
        private LevelModel levelModel;
        private AvatarsConfig avatarsConfig;
        private INetworkClient networkClient;
        private IAnalyticsService analyticsService;

        private string levelName;
        private string avatarCode;
        private bool canSendGameEndEvent;

        private int startGameFrame;
        private float startGameTime;

        [Inject]
        private void Construct(
            GameModel gameModel,
            LevelModel levelModel,
            AvatarsConfig avatarsConfig,
            INetworkClient networkClient,
            IAnalyticsService analyticsService,
            ISubscriber<AvatarChangeArgs> avatarChangeSubscriber)
        {
            this.gameModel = gameModel;
            this.levelModel = levelModel;
            this.networkClient = networkClient;
            this.avatarsConfig = avatarsConfig;
            this.analyticsService = analyticsService;

            networkClient.IsConnected.Subscribe(HandleConnected).AddTo(DisposeCancellationToken);
            avatarChangeSubscriber.Subscribe(HandleAvatarChanged).AddTo(DisposeCancellationToken);
        }

        private void HandleConnected(bool ok)
        {
            if (ok && !canSendGameEndEvent)
            {
                gameModel.UpGameCount();
                startGameFrame = Time.frameCount;
                startGameTime = Time.time;
                canSendGameEndEvent = true;
                SendLevelStartEvent();
            }
            else if (!ok && canSendGameEndEvent)
            {
                canSendGameEndEvent = false;
                SendLevelEndEvent();
            }
        }

        private void HandleAvatarChanged(AvatarChangeArgs args)
        {
            if (avatarsConfig.TryGetAvatarCode(args.avatarId, out var code))
            {
                avatarCode = code;
            }
        }

        private void SendLevelStartEvent()
        {
            levelName = levelModel.Level.name;
            var sessionName = networkClient.SessionInfo.Name;
            var roomId = string.IsNullOrEmpty(sessionName) ? string.Empty : sessionName[..Mathf.Min(sessionName.Length, 8)];
            var playerCount = networkClient.PlayerCount.Value;
            var gameCount = gameModel.GameCount;
            analyticsService.StartLevel(levelName, roomId, playerCount, gameCount);
        }

        private void SendLevelEndEvent()
        {
            var playerCount = Mathf.Max(0, networkClient.PlayerCount.Value - 1);
            var localtime = Mathf.RoundToInt(networkClient.LocalTime);
            var avatar = avatarCode;
            analyticsService.EndLevel(levelName, playerCount, localtime, avatar, GetFps());
        }

        private int GetFps()
        {
            return startGameTime == 0 ? 0 : Mathf.RoundToInt((Time.frameCount - startGameFrame) / (Time.time - startGameTime));
        }
    }
}