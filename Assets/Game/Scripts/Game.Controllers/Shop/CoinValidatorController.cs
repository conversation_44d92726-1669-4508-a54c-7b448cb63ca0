using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Services;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Shop
{
    public class CoinValidatorController : ControllerBase
    {
        private GameModel gameModel;
        private EconomyModel economyModel;
        private INotificationService notificationService;

        [Inject]
        private void Construct(GameModel gameModel, EconomyModel economyModel, INotificationService notificationService)
        {
            this.gameModel = gameModel;
            this.notificationService = notificationService;
            this.economyModel = economyModel;

            gameModel.IsInitialized.Where(ok => ok).Subscribe(_ => ValidateCoins().Forget()).AddTo(DisposeCancellationToken);
        }

        private async UniTaskVoid ValidateCoins()
        {
            if (economyModel.CoinAmount.Value < 500000)
            {
                return;
            }

            var extraCoinAmount = 1000 - economyModel.CoinAmount.Value;
            await economyModel.AddCoinAmount(extraCoinAmount, DisposeCancellationToken);
            notificationService.SendCoinValidation(gameModel.UserName, gameModel.UserId, Mathf.Abs(extraCoinAmount));
            GameLogger.Economy.Warn("Extra coin amount limit reached. Delta: {0}", extraCoinAmount);
        }
    }
}