using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.BlockShop;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.BlockShop
{
    public class BlockShopController : ControllerBase
    {
        private LevelModel levelModel;
        private PlayersModel playersModel;
        private EconomyModel economyModel;
        private IAudioClient audioClient;
        private BlockShopManager blockShopManager;
        private LobbySpaceManager lobbySpaceManager;
        private BlockInventoryModel blockInventoryModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            LevelModel levelModel,
            IAudioClient audioClient,
            PlayersModel playersModel,
            EconomyModel economyModel,
            BlockShopManager blockShopManager,
            LobbySpaceManager lobbySpaceManager,
            BlockInventoryModel blockInventoryModel)
        {
            this.levelModel = levelModel;
            this.audioClient = audioClient;
            this.playersModel = playersModel;
            this.economyModel = economyModel;
            this.blockShopManager = blockShopManager;
            this.lobbySpaceManager = lobbySpaceManager;
            this.blockInventoryModel = blockInventoryModel;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                return;
            }

            blockShopManager.OnClicked.Subscribe(HandleClicked).AddTo(player);
        }

        private void HandleLevelLoaded()
        {
            blockShopManager.ClearInstances();

            if (levelModel.IsLobbyLevel)
            {
                lobbySpaceManager.BlockShopNode.GetChildPoses().ForEach(blockShopManager.CreateInstance);
            }
        }

        private void HandleClicked(BlockInventoryView blockInventoryView)
        {
            PurchaseBlock(blockInventoryView).Forget();
        }

        private async UniTaskVoid PurchaseBlock(BlockInventoryView blockInventoryView)
        {
            var data = blockInventoryView.Data;
            if (economyModel.DiamondAmount.Value >= data.diamondPrice)
            {
                var result = await economyModel.AddDiamondAmount(-data.diamondPrice, DisposeCancellationToken);
                if (result.IsOk && LocalPlayer != null)
                {
                    audioClient.Play(AudioKeys.Goal, Vector3.zero, DisposeCancellationToken);
                    blockInventoryModel.PurchaseBlockInventory(data.id, data.amount);
                }
            }
            // Show Popup
        }
    }
}