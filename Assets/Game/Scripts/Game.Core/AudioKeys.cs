using System.Collections.Generic;
using Modules.Core;

namespace Game.Core
{
    public static class AudioKeys
    {
        private static readonly List<string> RicochetList = new() { Ricochet1, Ricochet2 };
        private static readonly List<string> ZombieVoiceList = new() { ZombieVoice1, <PERSON>Voice2, <PERSON>Voice3, <PERSON>Voice4, ZombieVoice5 };

        public const string PlaceBlock = "PlaceBlock";
        public const string Background = "Background";
        public const string IntroStartup = "IntroStartup";
        public const string VoxelTouch = "VoxelTouch";

        public const string GameStart = "GameStart";
        public const string GameEnd = "GameEnd";
        public const string Infected = "Infected";
        public const string ClearInfection = "ClearInfection";

        public const string ClaimChallenge = "ClaimChallenge";

        public const string AttachWing = "AttachWing";

        public const string InstallBuildZone = "InstallBuildZone";
        public const string UnInstallBuildZone = "UnInstallBuildZone";
        public const string ProhibitedBuildZone = "ProhibitedBuildZone";

        public const string HitDefault = "HitDefault";
        public const string ImpactDefault = "ImpactDefault";
        public const string DeadPlayer = "DeadPlayer";

        public const string LavaBurn = "LavaBurn";
        public const string Underwater = "Underwater";
        public const string Swim = "Swim";

        public const string GrenadeLauncherShoot = "GrenadeLauncherShoot";
        public const string GrenadeLauncherReload = "GrenadeLauncherReload";
        public const string GrenadeLauncherExplosion = "GrenadeLauncherExplosion";

        public const string Banjo1 = "Banjo1";
        public const string Banjo2 = "Banjo2";
        public const string BanjoEquip = "BanjoEquip";

        public const string NoAmmo = "NoAmmo";

        private const string Ricochet1 = "Ricochet1";
        private const string Ricochet2 = "Ricochet2";
        public static string RandomRicochet => RicochetList.RandomItem();

        public const string HammerSkyShoot = "Hammer_SkyShoot";
        public const string HammerAura = "Hammer_Aura";
        public const string MetalHit = "Metal_Hit";

        public const string FishHit = "FishHit";
        public const string Slap = "Slap";
        public const string FryingPanHit = "FryingPanHit";
        public const string BoneClubHit = "BoneClubHit";

        public const string ConsumeAmmo = "ConsumeAmmo";
        public const string ConsumeMedical = "ConsumeMedical";

        public const string GrappleGunFire = "GrappleGunFire";
        public const string GrappleGunAttachedHook = "GrappleGunAttachedHook";
        public const string GrappleGunFlyingHook = "GrappleGunFlyingHook";

        public const string WebShooterFire = "WebShooterFire";

        public const string ZombieAttack = "ZombieAttack";
        public const string ZombiePain = "ZombiePain";
        private const string ZombieVoice1 = "ZombieVoice1";
        private const string ZombieVoice2 = "ZombieVoice2";
        private const string ZombieVoice3 = "ZombieVoice3";
        private const string ZombieVoice4 = "ZombieVoice4";
        private const string ZombieVoice5 = "ZombieVoice5";

        public const string MonsterAttack0 = "MonsterAttack0";
        public const string MonsterScream0 = "MonsterScream0";

        public static string RandomZombieVoice => ZombieVoiceList.RandomItem();

        public const string MoneyBagCollect = "MoneyBagCollect";
        public const string MoneyBagDrop = "MoneyBagDrop";
        public const string CashRegister = "CashRegister";

        //brooms
        public const string VacuumFly = "VacuumFly";
        public const string JetFly = "JetFly";
        public const string FireworkFly = "FireworkFly";

        //racing
        public const string CheckpointTriggered = "Checkpoint";
        public const string RaceEnd = "RaceEnd";

        public const string Goal = "Goal";
        public const string WingitGlide = "WingitGlide";
        public const string WingitFlap = "WingitFlap";
        public const string ArmTouch = "ArmTouch";

        public const string CrownDance = "CrownDance";
        public const string WinRound = "WinRound";
        public const string TakeCrown = "TakeCrown";
        public const string LoseCrown = "LoseCrown";

        public const string BackpackAddItem = "BackpackAddItem";
        public const string BackpackDrop = "BackpackDrop";
        public const string BackpackEquip = "BackpackEquip";
        public const string BackpackEquipZip = "BackpackEquipZip";
        public const string BackpackRemoveItem = "BackpackRemoveItem";
        public const string BackpackFull = "BackpackFull";
        public const string BackpackOpenClose = "BackpackOpenClose";

        public const string ConverterMachine = "ConverterMachine";
    }
}