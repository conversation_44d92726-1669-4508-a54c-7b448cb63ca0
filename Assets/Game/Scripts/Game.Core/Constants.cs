using System;

namespace Game.Core
{
    public static class Constants
    {
        public const string CoinId = "REMZ";
        public const string CoinName = "Coins";

        public const string XpId = "XPGLOBAL";

        public const string DiamondId = "DIAMONDS";
        public const string DiamondName = "Diamonds";

        public const string YoutubeLink = "https://www.youtube.com/@animalblocks";
        public const string DiscordLink = "https://discord.gg/cXZmwrVvRP";
        public const string TiktokLink = "https://www.tiktok.com/@beastcraft_vr";
        public const string InfluencerLink = "https://www.youtube.com/@ebbydebbydoo";
        public const string GameLink = "https://www.meta.com/en-gb/experiences/animal-blocks/26317184997880598/";

        public static readonly TimeSpan ConnectionRetryInterval = TimeSpan.FromSeconds(5);

        public const int UiCullDistance = 10;
        public const int ObjectCullDistance = 30;

        public const int InfiniteBlockValue = -1;
    }
}