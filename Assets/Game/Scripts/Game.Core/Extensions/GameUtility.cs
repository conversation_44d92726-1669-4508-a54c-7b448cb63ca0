using System.Collections.Generic;
using UnityEngine;

namespace Game.Core.Extensions
{
    public static class GameUtility
    {
        public static void IgnoreCollisions(List<Collider> colliderList)
        {
            for (var i = 0; i < colliderList.Count; i++)
            {
                for (var j = i + 1; j < colliderList.Count; j++)
                {
                    if (colliderList[i] == colliderList[j])
                    {
                        continue;
                    }

                    Physics.IgnoreCollision(colliderList[i], colliderList[j]);
                }
            }
        }
    }
}