using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Views.Shared;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.BlockShop
{
    public class BlockShopView : Actor
    {
        [SerializeField] private GameObject viewNode;
        [SerializeField] private SimpleButton nextButton;
        [SerializeField] private SimpleButton previousButton;
        [SerializeField] private BlockShopContainer blockShopContainer;
        [SerializeField] private List<BlockCategoryButton> categoryButtonList;

        private bool isInitialized;
        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        public IObservable<BlockInventoryView> OnClicked => blockShopContainer.OnClicked;

        [Inject]
        private void Construct(DistanceCuller distanceCuller)
        {
            this.distanceCuller = distanceCuller;
        }

        private void OnEnable()
        {
            if (isInitialized)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }

            distanceCuller.AddTarget(viewNode, Constants.ObjectCullDistance);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            distanceCuller.RemoveTarget(viewNode);
        }

        public void Initialize(List<BlockData> blockDataList)
        {
            blockShopContainer.Initialize(blockDataList);

            if (IsActiveSelfAndInHierarchy)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }

            isInitialized = true;
        }

        public void Refresh()
        {
            if (!IsActiveSelfAndInHierarchy)
            {
                return;
            }

            SelectCategory(blockShopContainer.CurrentCategory);
        }

        private void SubscribeScreen()
        {
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();

            nextButton.OnClicked.Subscribe(_ => HandleNextButton()).AddTo(disableCancellationTokenSource.Token);
            previousButton.OnClicked.Subscribe(_ => HandlePreviousButton()).AddTo(disableCancellationTokenSource.Token);
            categoryButtonList.ForEach(b => b.OnClicked.Subscribe(_ => HandleCategoryClicked(b)).AddTo(disableCancellationTokenSource.Token));
        }

        private void SelectCategoryIfNone()
        {
            if (blockShopContainer.CurrentCategory != BlockCategory.None)
            {
                return;
            }

            categoryButtonList[0].Click();
        }

        private void SelectCategory(BlockCategory category)
        {
            blockShopContainer.SelectCategory(category);
        }

        private void HandleCategoryClicked(BlockCategoryButton button)
        {
            if (blockShopContainer.CurrentCategory == button.Category)
            {
                return;
            }

            SelectCategory(button.Category);
        }

        private void HandleNextButton()
        {
            blockShopContainer.SetNextPage();
        }

        private void HandlePreviousButton()
        {
            blockShopContainer.SetPreviousPage();
        }
    }
}