using System;
using System.Reactive.Subjects;
using DG.Tweening;
using Game.Views.Shared;
using Game.Views.Voxels;
using Modules.XR;
using TMPro;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using XRSimpleInteractable = Modules.XR.XRSimpleInteractable;

namespace Game.Views.BlockShop
{
    public class BlockInventoryView : XRSimpleInteractable
    {
        [Header("BlockInventoryView")]
        [SerializeField] private TMP_Text nameText;
        [SerializeField] private TMP_Text priceText;
        [SerializeField] private GameObject textsNode;
        [SerializeField] private Transform interactableNode;
        [SerializeField] private BlockProvider blockProvider;
        [SerializeField] private float grabEndThreshold = 0.5f;

        private readonly ISubject<BlockInventoryView> onClicked = new Subject<BlockInventoryView>();

        private IXRInput xrInput;
        private Tweener scaleTweener;
        private Vector3 grabDeltaPosition;
        private Vector3 grabInitialPosition;
        private Vector3 originLocalPosition;
        private DistanceCuller distanceCuller;

        public BlockData Data { get; private set; }
        public IXRSelectInteractor GrabInteractor { get; private set; }
        public IObservable<BlockInventoryView> OnClicked => onClicked;

        [Inject]
        private void Construct(IXRInput xrInput, DistanceCuller distanceCuller)
        {
            this.xrInput = xrInput;
            this.distanceCuller = distanceCuller;
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            distanceCuller.AddTarget(textsNode);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ResetGrabbing();
            distanceCuller.RemoveTarget(textsNode);
        }

        private void Update()
        {
            UpdateGrabbing();
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            interactableNode.DOScale(1.2f, 0.2f).SetEase(Ease.OutBack);
            xrInput.SendHapticImpulse(args.interactorObject.GetHandType(), HapticImpulse.ShortDurationLowAmplitude);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            interactableNode.DOScale(1, 0.2f).SetEase(Ease.OutBack);
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);
            GrabInteractor = args.interactorObject;
            grabInitialPosition = GrabInteractor.transform.position;
            grabDeltaPosition = interactableNode.position - grabInitialPosition;
        }

        protected override void OnSelectExited(SelectExitEventArgs args)
        {
            base.OnSelectExited(args);
            ResetGrabbing();
        }

        public void Render(BlockData data, float offset = 0)
        {
            if (data == null || data == Data)
            {
                return;
            }

            Clear();

            Data = data;
            blockProvider.Create(data, 0.5f);
            transform.localPosition = new Vector3(offset, 0, 0);
            nameText.text = data.voxel.title;
            priceText.text = $"{data.diamondPrice} <sprite name=Diamond>   {data.amount}";
        }

        private void Clear()
        {
            blockProvider.Destroy();
        }

        private void Click()
        {
            if (Data == null)
            {
                return;
            }

            onClicked.OnNext(this);
        }

        private void UpdateGrabbing()
        {
            if (GrabInteractor == null)
            {
                return;
            }

            var grabCurrentPosition = GrabInteractor.transform.position;
            var distance = Vector3.Distance(grabInitialPosition, grabCurrentPosition);

            if (distance > grabEndThreshold)
            {
                Click();
                ResetGrabbing();
            }
            else
            {
                interactableNode.position = grabCurrentPosition + grabDeltaPosition;
            }
        }

        private void ResetGrabbing()
        {
            GrabInteractor = null;
            interactableNode.localPosition = Vector3.zero;
        }
    }
}