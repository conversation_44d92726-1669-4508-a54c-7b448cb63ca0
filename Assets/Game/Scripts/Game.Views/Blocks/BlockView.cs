using Modules.Core;
using TMPro;
using UnityEngine;
using VContainer;

namespace Game.Views.Blocks
{
    public class BlockView : Actor
    {
        [SerializeField] private Renderer selfRenderer;
        [SerializeField] private Transform customBlockParent;
        [SerializeField] private TMP_Text blockNameText;
        [SerializeField] private TMP_Text blockCountText;

        private CustomBlock customBlock;
        private BlocksManager blocksManager;
        private int id;

        [Inject]
        private void Construct(BlocksManager blocksManager)
        {
            this.blocksManager = blocksManager;
        }

        public void Initialize(int id, string title, Texture2D texture, bool useNameText, bool useCountText)
        {
            DestroyCustomBlock();

            this.id = id;
            selfRenderer.gameObject.SetActive(true);
            selfRenderer.material.mainTexture = texture;
            InitializeTexts(title, useNameText, useCountText);
        }

        public void Initialize(int id, string title, CustomBlock customBlock, bool useNameText, bool useCountText)
        {
            DestroyCustomBlock();

            this.id = id;
            selfRenderer.gameObject.SetActive(false);
            InitializeTexts(title, useNameText, useCountText);

            this.customBlock = customBlock;
            customBlock.transform.SetParent(customBlockParent, false);
            customBlock.transform.localPosition = Vector3.zero;
        }

        public void SetBlockCount(int blockCount)
        {
            blockCountText.text = blockCount >= 0 ? blockCount.ToString() : "∞";
        }

        public void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        private void InitializeTexts(string title, bool useNameText, bool useCountText)
        {
            blockNameText.gameObject.SetActive(useNameText);
            blockCountText.gameObject.SetActive(useCountText);

            if (useNameText)
            {
                blockNameText.text = title;
            }

            if (useCountText)
            {
                blockCountText.text = string.Empty;
            }
        }

        private void DestroyCustomBlock()
        {
            if (customBlock == null)
            {
                return;
            }

            blocksManager.DestroyCustomBlock(id, customBlock);
            customBlock = null;
        }
    }
}