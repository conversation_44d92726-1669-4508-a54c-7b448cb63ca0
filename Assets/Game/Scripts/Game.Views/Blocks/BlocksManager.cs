using System.Collections.Generic;
using Game.Core;
using Game.Views.Voxels;
using Modules.Core;
using UnityEngine;
using VContainer;
using VoxelPlay;

namespace Game.Views.Blocks
{
    public class BlocksManager : MonoBehaviour
    {
        [SerializeField] private BlockView blockViewPrefab;

        private VoxelConfig voxelConfig;
        private IObjectResolver objectResolver;
        private ComponentPool<BlockView> blockViewPool;
        private readonly Dictionary<int, ComponentPool<CustomBlock>> customBlockPool = new();

        private ComponentPool<BlockView> BlockViewPool => blockViewPool ??= new ComponentPool<BlockView>(blockViewPrefab, transform, objectResolver);

        [Inject]
        private void Construct(IObjectResolver objectResolver, VoxelConfig voxelConfig)
        {
            this.voxelConfig = voxelConfig;
            this.objectResolver = objectResolver;
        }

        public BlockView CreateBlock(int id, bool useNameText, bool useCountText, Transform parent)
        {
            if (!voxelConfig.TryGetBlockData(id, out var blockData))
            {
                return null;
            }

            var view = BlockViewPool.Get();
            view.SetParent(parent);

            var voxelDef = blockData.voxel;
            if (voxelDef.renderType == RenderType.Custom)
            {
                if (!customBlockPool.ContainsKey(id))
                {
                    if (!voxelDef.prefab.TryGetComponent(out CustomBlock customBlockPrefab))
                    {
                        GameLogger.Game.Error("Custom block prefab is missing CustomBlock component. Block id: {0}", id);
                    }

                    customBlockPool[id] = new ComponentPool<CustomBlock>(customBlockPrefab, transform, objectResolver, 0);
                }

                var customBlock = customBlockPool[id].Get();
                view.Initialize(id, voxelDef.title, customBlock, useNameText, useCountText);
            }
            else
            {
                view.Initialize(id, voxelDef.title, voxelDef.textureSide, useNameText, useCountText);
            }

            return view;
        }

        public void DestroyBlock(BlockView blockView)
        {
            if (blockView != null)
            {
                BlockViewPool.Release(blockView);
            }
        }

        public void DestroyCustomBlock(int id, CustomBlock customBlock)
        {
            if (!customBlockPool.TryGetValue(id, out var pool))
            {
                return;
            }

            pool.Release(customBlock);
        }
    }
}