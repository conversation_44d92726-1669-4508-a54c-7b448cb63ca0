using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.SmallShop
{
    public class SmallShopManager : Actor
    {
        [SerializeField] private SmallShopView smallShopViewPrefab;

        private readonly ISubject<InventoryClickArgs> onInventoryClicked = new Subject<InventoryClickArgs>();
        private readonly ISubject<InventoryCategory> onInventoryReset = new Subject<InventoryCategory>();

        private IObjectResolver objectResolver;
        private ComponentPool<SmallShopView> smallShopPool;
        private CancellationTokenSource disableCancellationTokenSource;

        private ComponentPool<SmallShopView> SmallShopPool => smallShopPool ??= new ComponentPool<SmallShopView>(smallShopViewPrefab, transform, objectResolver, 0);

        public IObservable<InventoryClickArgs> OnInventoryClicked => onInventoryClicked;
        public IObservable<InventoryCategory> OnInventoryReset => onInventoryReset;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        private void OnDestroy()
        {
            disableCancellationTokenSource.CancelAndDispose();
        }

        public void Create(List<Pose> poseList, List<ShopInventoryData> inventoryList)
        {
            DestroyAll();

            disableCancellationTokenSource = new CancellationTokenSource();
            foreach (var pose in poseList)
            {
                var smallShopView = SmallShopPool.Get();
                smallShopView.Initialize(inventoryList);
                smallShopView.SetPose(pose);
                smallShopView.OnClicked.Subscribe(HandleInventoryClicked).AddTo(disableCancellationTokenSource.Token);
                smallShopView.OnInventoryReset.Subscribe(HandleInventoryReset).AddTo(disableCancellationTokenSource.Token);
            }
        }

        public void DestroyAll()
        {
            disableCancellationTokenSource.CancelAndDispose();
            SmallShopPool.ReleaseAll();
        }

        public void Refresh()
        {
            SmallShopPool.ActivePoolItems.ForEach(s => s.Refresh());
        }

        private void HandleInventoryClicked(InventoryView inventoryView)
        {
            onInventoryClicked.OnNext(new InventoryClickArgs(inventoryView.Inventory, inventoryView.GrabInteractor, Pose.identity));
        }

        private void HandleInventoryReset(InventoryCategory category)
        {
            onInventoryReset.OnNext(category);
        }
    }
}