using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Core.Data;
using Game.Views.Shared;
using Modules.Core;
using TMPro;
using UnityEngine;
using VContainer;

namespace Game.Views.SmallShop
{
    public class SmallShopView : Actor
    {
        [SerializeField] private GameObject viewNode;
        [SerializeField] private TMP_Text infoText;
        [SerializeField] private SimpleButton nextButton;
        [SerializeField] private SimpleButton previousButton;
        [SerializeField] private SimpleButton resetInventoryButton;
        [SerializeField] private ShopItemsContainer shopItemsContainer;
        [SerializeField] private List<CategoryButton> categoryButtonList;

        private bool isInitialized;
        private DistanceCuller distanceCuller;
        private CancellationTokenSource disableCancellationTokenSource;

        private readonly ISubject<InventoryCategory> onInventoryReset = new Subject<InventoryCategory>();

        public IObservable<InventoryView> OnClicked => shopItemsContainer.OnClicked;
        public IObservable<InventoryCategory> OnInventoryReset => onInventoryReset;

        [Inject]
        private void Construct(DistanceCuller distanceCuller)
        {
            this.distanceCuller = distanceCuller;
        }

        private void OnEnable()
        {
            if (isInitialized)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }

            distanceCuller.AddTarget(infoText.gameObject);
            distanceCuller.AddTarget(viewNode, Constants.ObjectCullDistance);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            distanceCuller.RemoveTarget(infoText.gameObject);
            distanceCuller.RemoveTarget(viewNode);
        }

        public void Initialize(List<ShopInventoryData> inventoryList)
        {
            shopItemsContainer.Initialize(inventoryList);

            if (IsActiveSelfAndInHierarchy)
            {
                SubscribeScreen();
                SelectCategoryIfNone();
            }

            isInitialized = true;
        }

        public void Refresh()
        {
            if (!IsActiveSelfAndInHierarchy)
            {
                return;
            }

            SelectCategory(shopItemsContainer.CurrentCategory);
        }

        private void SubscribeScreen()
        {
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();

            nextButton.OnClicked.Subscribe(_ => HandleNextButton()).AddTo(disableCancellationTokenSource.Token);
            previousButton.OnClicked.Subscribe(_ => HandlePreviousButton()).AddTo(disableCancellationTokenSource.Token);
            resetInventoryButton.OnClicked.Subscribe(_ => HandleInventoryReset()).AddTo(disableCancellationTokenSource.Token);
            categoryButtonList.ForEach(b => b.OnClicked.Subscribe(HandleCategoryClicked).AddTo(disableCancellationTokenSource.Token));
        }

        private void SelectCategoryIfNone()
        {
            if (shopItemsContainer.CurrentCategory != InventoryCategory.None)
            {
                return;
            }

            categoryButtonList[0].Click();
        }

        private void SelectCategory(InventoryCategory category)
        {
            var isOutfitCategory = category is InventoryCategory.Hat or InventoryCategory.Suit or InventoryCategory.Badge;
            resetInventoryButton.gameObject.SetActive(isOutfitCategory);

            shopItemsContainer.SelectCategory(category);
        }

        private void HandleCategoryClicked(CategoryButton button)
        {
            if (shopItemsContainer.CurrentCategory == button.Category)
            {
                return;
            }

            SelectCategory(button.Category);
        }

        private void HandleNextButton()
        {
            shopItemsContainer.SetNextPage();
        }

        private void HandlePreviousButton()
        {
            shopItemsContainer.SetPreviousPage();
        }

        private void HandleInventoryReset()
        {
            onInventoryReset.OnNext(shopItemsContainer.CurrentCategory);
        }
    }
}