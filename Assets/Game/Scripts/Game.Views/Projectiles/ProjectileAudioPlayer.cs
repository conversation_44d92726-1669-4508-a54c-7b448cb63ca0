using UnityEngine;

namespace Game.Views.Projectiles
{
    public class ProjectileAudioPlayer : MonoBehaviour
    {
        [SerializeField] private AudioSource audioSource;
        [SerializeField] [Range(0.01f, 10f)] private float pitchRandomMultiplier = 1.2f;

        private void OnEnable()
        {
            audioSource.pitch = Random.value < 0.5f ? Random.Range(1 / pitchRandomMultiplier, 1) : Random.Range(1, pitchRandomMultiplier);
            audioSource.Play();
        }
    }
}