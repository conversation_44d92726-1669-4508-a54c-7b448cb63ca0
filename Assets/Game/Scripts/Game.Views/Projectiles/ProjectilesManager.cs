using System.Collections.Generic;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Projectiles
{
    public class ProjectilesManager : MonoBehaviour
    {
        [SerializeField] private List<ProjectileView> projectilePrefabList;

        private IObjectResolver objectResolver;
        private readonly Dictionary<ProjectileId, ComponentPool<ProjectileView>> projectilePool = new();

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public ProjectileView CreateProjectile(ProjectileId id)
        {
            if (!projectilePool.ContainsKey(id))
            {
                var prefab = projectilePrefabList.Find(x => x.Id == id);
                projectilePool[id] = new ComponentPool<ProjectileView>(prefab, transform, objectResolver);
            }

            return projectilePool[id].Get();
        }

        public void DestroyProjectile(ProjectileView view)
        {
            if (view == null || !projectilePool.TryGetValue(view.Id, out var pool))
            {
                return;
            }

            pool.Release(view);
        }
    }
}