using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Projectiles
{
    public class ProjectileView : Actor
    {
        [SerializeField] private ProjectileId id;
        [SerializeField] private float speed = 100f;
        [SerializeField] private float lifeTimeAfterImpact = 2f;
        [SerializeField] private GameObject projectileObject;
        [SerializeField] private GameObject defaultImpactObject;
        [SerializeField] private GameObject enemyImpactObject;

        private float duration;
        private float startTime;
        private bool isUpdating;
        private bool isEnemyImpact;
        private Vector3 startPosition;
        private Vector3 targetPosition;
        private ProjectilesManager projectilesManager;

        public ProjectileId Id => id;

        [Inject]
        private void Construct(ProjectilesManager projectilesManager)
        {
            this.projectilesManager = projectilesManager;
        }

        private void Awake()
        {
            DisableAll();
        }

        private void OnDisable()
        {
            DisableAll();
        }

        private void Update()
        {
            if (!isUpdating)
            {
                return;
            }

            var deltaTime = (Time.time - startTime) / duration;
            if (deltaTime <= 1)
            {
                transform.position = Vector3.Lerp(startPosition, targetPosition, deltaTime);
            }
            else
            {
                transform.position = targetPosition;

                if (targetPosition.IsNotZero())
                {
                    EnableImpact();
                    ReturnToPool(lifeTimeAfterImpact);
                }
                else
                {
                    ReturnToPool();
                }

                isUpdating = false;
            }
        }

        public void Fire(Vector3 startPosition, Vector3 targetPosition, ProjectileImpactType projectileImpactType)
        {
            isEnemyImpact = projectileImpactType == ProjectileImpactType.Enemy;
            transform.forward = (targetPosition - startPosition).normalized;
            this.startPosition = transform.position = startPosition;
            this.targetPosition = targetPosition;
            duration = Vector3.Distance(startPosition, targetPosition) / speed;
            startTime = Time.time;
            isUpdating = true;

            EnableProjectile();
        }

        private void EnableProjectile()
        {
            projectileObject.SetActive(true);
            defaultImpactObject.SetActive(false);
            enemyImpactObject.SetActive(false);
        }

        private void EnableImpact()
        {
            projectileObject.SetActive(false);
            defaultImpactObject.SetActive(!isEnemyImpact);
            enemyImpactObject.SetActive(isEnemyImpact);
        }

        private void DisableAll()
        {
            projectileObject.SetActive(false);
            defaultImpactObject.SetActive(false);
            enemyImpactObject.SetActive(false);
        }

        private void ReturnToPool(float delay = 0)
        {
            if (delay > 0)
            {
                UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(delay)).Subscribe(_ => projectilesManager.DestroyProjectile(this)).AddTo(destroyCancellationToken);
            }
            else
            {
                projectilesManager.DestroyProjectile(this);
            }
        }
    }
}