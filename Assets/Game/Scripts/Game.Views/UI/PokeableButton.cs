using System;
using System.Reactive.Subjects;
using Modules.Core;
using Modules.UI;
using TMPro;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using XRSimpleInteractable = Modules.XR.XRSimpleInteractable;

namespace Game.Views.UI
{
    public class PokeableButton : XRSimpleInteractable
    {
        [SerializeField] private TMP_Text titleText;
        [SerializeField] private Transform visualNode;
        [SerializeField] private Renderer visualRenderer;
        [SerializeField] private Material defaultMaterial;
        [SerializeField] private Material selectMaterial;
        [SerializeField] private float pressedShift;
        [SerializeField] private bool isReleaseDetection;

        private readonly ISubject<PokeableButton> onClick = new Subject<PokeableButton>();
        private IAudioClient audioClient;

        public IObservable<PokeableButton> OnClick => onClick;

        [Inject]
        private void Construct(IAudioClient audioClient)
        {
            this.audioClient = audioClient;
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            for (var i = interactorsSelecting.Count - 1; i >= 0; i--)
            {
                interactionManager.SelectCancel(interactorsSelecting[i], this);
            }
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);
            visualRenderer.sharedMaterial = selectMaterial;
            visualNode.localPosition = visualNode.localPosition.SetY(-pressedShift);

            if (!isReleaseDetection)
            {
                Click();
            }
        }

        protected override void OnSelectExited(SelectExitEventArgs args)
        {
            base.OnSelectExited(args);
            if (gameObject == null)
            {
                return;
            }

            visualNode.localPosition = visualNode.localPosition.SetY(0);
            visualRenderer.sharedMaterial = defaultMaterial;

            if (isReleaseDetection)
            {
                Click();
            }
        }

        public void SetMessage(string message)
        {
            titleText.text = message;
        }

        private void Click()
        {
            audioClient?.Play(UIAudioKeys.ButtonClick, transform.position, destroyCancellationToken);
            onClick.OnNext(this);
        }
    }
}