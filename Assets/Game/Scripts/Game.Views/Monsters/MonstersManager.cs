using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Monsters
{
    public class MonstersManager : MonoBehaviour
    {
        private readonly Dictionary<byte, ComponentPool<MonsterAvatar>> avatarPoolList = new();
        private readonly Dictionary<byte, ComponentPool<MonsterRagdoll>> ragdollPoolList = new();
        private readonly Dictionary<int, MonsterActor> monsterList = new();
        private IObjectResolver objectResolver;
        private INetworkClient networkClient;
        private MonstersConfig monstersConfig;

        [Inject]
        private void Construct(INetworkClient networkClient, MonstersConfig monstersConfig, IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
            this.monstersConfig = monstersConfig;
            this.networkClient = networkClient;
        }

        private void Awake()
        {
            networkClient.OnNetworkActorSpawned.Subscribe(x => HandleNetworkActorSpawned(x.actor)).AddTo(destroyCancellationToken);
            networkClient.OnNetworkActorDespawned.Subscribe(x => HandleNetworkActorDespawned(x.actor)).AddTo(destroyCancellationToken);
        }

        private void OnDestroy()
        {
            monsterList.Clear();
            avatarPoolList.Clear();
            ragdollPoolList.Clear();
        }

        public MonsterActor CreateRandomMonster(Vector3 position, Quaternion rotation)
        {
            MonsterActor monster = null;

            var networkedMonster = monstersConfig.GetNetworkObjectPrefab(0, true);
            if (networkedMonster == null)
            {
                return null;
            }

            networkClient.Spawn(networkedMonster, position, rotation, (_, obj) =>
            {
                if (obj.TryGetComponent(out monster))
                {
                    monster.AvatarId = monstersConfig.GetRandomAvatarId();
                }
            });

            return monster;
        }

        public void CreateMonster(int monsterDataIndex, Vector3 position, Quaternion rotation, byte code)
        {
            if (!monstersConfig.TryGetMonsterData(code, out var data))
            {
                return;
            }

            if (monsterList.ContainsKey(monsterDataIndex))
            {
                return;
            }

            var networkedMonster = monstersConfig.GetNetworkObjectPrefab(code);
            if (networkedMonster == null)
            {
                return;
            }

            networkClient.Spawn(networkedMonster, position, rotation, (_, obj) =>
            {
                if (obj.TryGetComponent(out MonsterActor monster))
                {
                    monster.AvatarId = code;
                    monster.MonsterDataIndex = monsterDataIndex;
                    monster.SetHealthNetworked(data.Health);
                    monster.ChaseSpeed = data.ChaseSpeed;
                    monster.PatrolSpeed = data.PatrolSpeed;
                    monster.AttackDamage = data.Damage;
                }
            });
        }

        public MonsterAvatar CreateAvatar(byte code, Transform parent)
        {
            if (!avatarPoolList.ContainsKey(code) && monstersConfig.TryGetAvatarPrefab(code, out var prefab))
            {
                avatarPoolList[code] = new ComponentPool<MonsterAvatar>(prefab, transform, objectResolver);
            }

            var avatar = avatarPoolList[code].Get();
            avatar.SetParent(parent);

            return avatar;
        }

        public MonsterRagdoll CreateRagdoll(byte code)
        {
            if (!ragdollPoolList.ContainsKey(code) && monstersConfig.TryGetRagdollPrefab(code, out var prefab))
            {
                ragdollPoolList[code] = new ComponentPool<MonsterRagdoll>(prefab, transform, objectResolver);
                var ragdoll = ragdollPoolList[code].Get();
                ragdoll.SetCode(code);

                return ragdoll;
            }

            return null;
        }

        public void DestroyMonster(MonsterActor monster)
        {
            if (monster == null)
            {
                return;
            }

            networkClient.Despawn(monster.Object);
        }

        public void DestroyAvatar(MonsterAvatar avatar)
        {
            if (avatar == null || !avatarPoolList.TryGetValue(avatar.Code, out var pool))
            {
                return;
            }

            pool.Release(avatar);
        }

        public void DestroyRagdoll(MonsterRagdoll ragdoll)
        {
            if (ragdoll == null || !ragdollPoolList.TryGetValue(ragdoll.Code, out var pool))
            {
                return;
            }

            pool.Release(ragdoll);
        }

        public void KillAllMonsters()
        {
            var monsters = new List<MonsterActor>(monsterList.Values);
            for (var i = monsters.Count - 1; i >= 0; i--)
            {
                var monster = monsters[i];
                monster.Damage(byte.MaxValue, 1500 * -monster.transform.forward);
            }
        }

        public int GetMonsterCountInArea(Vector3 origin, float radius)
        {
            var sqrRadius = radius * radius;
            var count = 0;

            var monsters = new List<MonsterActor>(monsterList.Values);
            for (var i = 0; i < monsters.Count; i++)
            {
                var monster = monsters[i];
                var distance = (monster.transform.position - origin).sqrMagnitude;

                if (distance < sqrRadius)
                {
                    count++;
                }
            }

            return count;
        }

        private void HandleNetworkActorSpawned(NetworkActor actor)
        {
            if (actor is not MonsterActor monsterActor)
            {
                return;
            }

            monsterList.TryAdd(monsterActor.MonsterDataIndex, monsterActor);
        }

        private void HandleNetworkActorDespawned(NetworkActor actor)
        {
            if (actor is not MonsterActor monsterActor)
            {
                return;
            }

            var monsterIndex = monsterActor.MonsterDataIndex;
            if (monsterList.ContainsKey(monsterIndex))
            {
                monsterList.Remove(monsterIndex);
            }
        }
    }
}