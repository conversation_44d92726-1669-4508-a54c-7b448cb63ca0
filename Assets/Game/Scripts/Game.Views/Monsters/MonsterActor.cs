using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Fusion.Addons.Physics;
using Game.Core;
using Game.Views.Effects;
using Game.Views.InGameRewards;
using Game.Views.Players;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Monsters
{
    public class MonsterActor : NetworkActor
    {
        [Header("Monster")]
        [SerializeField] private Rigidbody selfRigidbody;
        [SerializeField] private Collider selfCollider;
        [SerializeField] private NetworkTransform networkTransform;
        [SerializeField] private NetworkRigidbody3D networkRigidbody3D;
        [SerializeField] private Transform avatarNode;
        [SerializeField] private Transform audioNode;
        [SerializeField] private EffectId deathEffectId;

        private IPublisher<MonsterDamageArgs> damagePublisher;
        private IPublisher<RewardArgs> rewardPublisher;
        private MonstersManager monstersManager;
        private PlayersModel playersModel;
        private IAudioClient audioClient;
        private MonsterAvatar avatar;
        private bool ragdollCreated;
        private EffectsManager effectsManager;
        private MonstersConfig monstersConfig;
        
        public bool IsAlive => Health > 0;
        public Vector3 Center => avatar == null ? Vector3.zero : avatar.Center;
        public byte Health {get; private set;}

        [Networked] [OnChangedRender(nameof(ChangeAvatarId))]
        public byte AvatarId { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeHealthLocal))]
        private byte HealthNetworked { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeMonsterState))]
        public MonsterState State { get; set; }

        [Networked] public float ChaseSpeed { get; set; }
        [Networked] public byte AttackDamage { get; set; }
        [Networked] public float PatrolSpeed { get; set; }
        [Networked] public int MonsterDataIndex { get; set; }

        [Inject]
        private void Construct(
            IAudioClient audioClient,
            MonstersManager monstersManager,
            PlayersModel playersModel,
            IPublisher<MonsterDamageArgs> damagePublisher,
            EffectsManager effectsManager,
            MonstersConfig monstersConfig,
            IPublisher<RewardArgs> rewardPublisher)
        {
            this.damagePublisher = damagePublisher;
            this.monstersManager = monstersManager;
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            this.effectsManager = effectsManager;
            this.monstersConfig = monstersConfig;
            this.rewardPublisher = rewardPublisher;
        }

        public override void Spawned()
        {
            base.Spawned();
            ragdollCreated = false;
            ChangeAvatarId();
            ChangeMonsterState();
            ChangeHealthLocal();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            monstersManager.DestroyAvatar(avatar);
            avatar = null;
        }

        public void Teleport(Vector3 position)
        {
            transform.position = position;
            if (networkTransform != null)
            {
                networkTransform.Teleport(position, transform.rotation);
            }
        }

        public void PlayAudio(string audioKey, bool oneShot = false)
        {
            if (oneShot)
            {
                audioClient.PlayOneInstance(audioKey, avatarNode, destroyCancellationToken);
            }
            else
            {
                audioClient.Play(audioKey, avatarNode, destroyCancellationToken);
            }
        }

        public bool IsKillable(int damage)
        {
            return Health - damage <= 0;
        }

        public void Damage(byte damage, Vector3 damageDirection)
        {
            if (Health == byte.MaxValue)
            {
                return;
            }

            SendRpcSafe(() => DamageRpc(damage, damageDirection));
        }
        
        public void SetHealthNetworked(byte health)
        {
            if (HasStateAuthority)
            {
                HealthNetworked = health;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void DamageRpc(byte damage, Vector3 damageDirection, RpcInfo info = default)
        {
            var newHealth = (byte)Mathf.Max(0, Health - damage);
            SetHealthNetworked(newHealth);
            if (newHealth > 0)
            {
                damagePublisher.Publish(new MonsterDamageArgs(this, damage, damageDirection, info.Source));
            }
            else
            {
                rewardPublisher.Publish(new RewardArgs(avatar.Code, transform.position));
                KillRpc(damageDirection, info);
            }
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void KillRpc(Vector3 damageDirection, RpcInfo info = default)
        {
            SetKill(damageDirection, info.Source).Forget();
        }

        private async UniTaskVoid SetKill(Vector3 damageDirection, PlayerRef killer)
        {
            if (avatar != null)
            {
                avatar.SetActive(false);
            }

            if (IsInRange())
            {
                CreateRagdollAndAddForce(damageDirection, transform.position);
            }

            monstersManager.DestroyMonster(this);

            await UniTask.Delay(TimeSpan.FromSeconds(3), cancellationToken: DespawnCancellationToken);

            if (Runner != null)
            {
                monstersManager.DestroyMonster(this);
            }
        }

        private void CreateRagdollAndAddForce(Vector3 force, Vector3 position)
        {
            if (ragdollCreated)
            {
                return;
            }

            ragdollCreated = true;

            var ragdoll = monstersManager.CreateRagdoll(avatar.Code);
            if (ragdoll != null)
            {
                ragdoll.transform.SetPositionAndRotation(transform.position, transform.rotation);
                ragdoll.SetPose(avatar.GetNodeList());
                ragdoll.AddForce(force);
            }
            else if (deathEffectId != EffectId.None)
            {
                effectsManager.CreateEffect(deathEffectId, new Pose(position, Quaternion.identity), 4);
            }
        }

        private void ChangeAvatarId()
        {
            monstersManager.DestroyAvatar(avatar);
            avatar = monstersManager.CreateAvatar(AvatarId, avatarNode);
        }

        private string GetMonsterDamageSoundKey()
        {
            if (monstersConfig.TryGetMonsterData(avatar.Code, out var monsterData) && !string.IsNullOrEmpty(monsterData.DamageReceivedSoundKey))
            {
                return monsterData.DamageReceivedSoundKey;
            }

            return AudioKeys.ZombiePain;
        }

        private void ChangeHealthLocal()
        {
            if (IsInRange() && IsAlive && HealthNetworked < Health)
            {
                audioClient.Play(GetMonsterDamageSoundKey(), audioNode.position, destroyCancellationToken);
            }
            Health = HealthNetworked;
        }

        private void ChangeMonsterState()
        {
            if (avatar == null)
            {
                return;
            }

            switch (State)
            {
                case MonsterState.Idle:
                    avatar.PlayIdleAnimation();
                    break;
                case MonsterState.Patrol:
                    avatar.PlayWalkAnimation();
                    break;
                case MonsterState.Chase:
                    avatar.PlayRunAnimation();
                    break;
                case MonsterState.Attack:
                    avatar.PlayAttackAnimation();
                    break;
                case MonsterState.DamageReceive:
                    avatar.PlayDamageReceiveAnimation();
                    break;
            }
        }

        private bool IsInRange()
        {
            return playersModel.IsLocalPlayerInRadius(transform.position, 30);
        }
    }
}