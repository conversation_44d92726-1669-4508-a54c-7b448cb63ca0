using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Players;
using Game.Views.PlayerUI.Backpack;
using Game.Views.PlayerUI.Blocks;
using Game.Views.PlayerUI.Wrist;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.PlayerUI
{
    public class PlayerMenu : Actor
    {
        [SerializeField] private GameObject root;
        [SerializeField] private BodyMenu bodyMenu;
        [SerializeField] private HandsMenu handsMenu;
        [SerializeField] private WristMenu wristMenu;
        [SerializeField] private BlocksMenu blocksMenu;
        [SerializeField] private BackpackMenu backpackMenu;
        [SerializeField] private MoneyBagCollectMenu moneyBagCollectMenu;
        [SerializeField] private InteractablesMenu interactablesMenu;

        private IXRPlayer xrPlayer;

        public BodyMenu BodyMenu => bodyMenu;
        public HandsMenu HandsMenu => handsMenu;
        public WristMenu WristMenu => wristMenu;
        public BlocksMenu BlocksMenu => blocksMenu;
        public BackpackMenu BackpackMenu => backpackMenu;
        public MoneyBagCollectMenu MoneyBagCollectMenu => moneyBagCollectMenu;
        public InteractablesMenu InteractablesMenu => interactablesMenu;

        [Inject]
        private void Construct(IObjectResolver objectResolver, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;

            objectResolver.InjectGameObject(root);
        }

        private void Awake()
        {
            interactablesMenu.IsMenuShowed.Where(ok => ok).Subscribe(_ => blocksMenu.HideMenu()).AddTo(destroyCancellationToken);
            blocksMenu.IsMenuShowed.Where(ok => ok).Subscribe(_ => interactablesMenu.HideMenu()).AddTo(destroyCancellationToken);
        }

        public void SetScale(float scale)
        {
            bodyMenu.SetScale(scale);
            handsMenu.SetScale(scale);
            wristMenu.SetScale(scale);
            blocksMenu.SetScale(scale);
            backpackMenu.SetScale(scale);
            interactablesMenu.SetScale(scale);
            moneyBagCollectMenu.SetScale(scale);
        }

        public void SetPose(PlayerActor player)
        {
            if (!player.TryGetAvatarHandPose(out var leftHandPose, out var rightHandPose))
            {
                return;
            }

            handsMenu.UpdatePose(leftHandPose, rightHandPose);
            wristMenu.UpdatePose(xrPlayer.LeftHandNode.position, xrPlayer.LeftHandNode.rotation);
            backpackMenu.SetPose(xrPlayer.BodyPosition, xrPlayer.BodyRotation);
            moneyBagCollectMenu.SetPose(xrPlayer.BodyPosition, xrPlayer.BodyRotation);
            blocksMenu.SetPose(xrPlayer.BodyPosition, xrPlayer.BodyRotation, xrPlayer.HeadNode.position, xrPlayer.HeadNode.rotation);
            interactablesMenu.SetPose(xrPlayer.BodyPosition, xrPlayer.BodyRotation, xrPlayer.HeadNode.position, xrPlayer.HeadNode.rotation);
        }
    }
}