using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.PlayerUI.Blocks
{
    public class BlocksMenu : Actor
    {
        private const int MaxRowCount = 3;
        private const int MaxColumnCount = 4;
        private const int BlocksOnPage = MaxRowCount * MaxColumnCount;
        private const float RowOffset = 16;
        private const float ColumnOffset = 16;
        private const float BubbleDiameter = 0.1f;
        private const int NextPageKey = -1;
        private const int BackPageKey = -2;
        private const int Timeout = 15;

        [SerializeField] private BlockWidgetSocket blockWidgetSocketPrefab;
        [SerializeField] private BlockWidget blockWidgetPrefab;
        [SerializeField] private SimpleWidget nextPageWidget;
        [SerializeField] private SimpleWidget backPageWidget;
        [SerializeField] private Transform root;
        [SerializeField] private float translationSpeed = 2;
        [SerializeField] private float rotationSpeed = 2;
        [SerializeField] private float angleLimit = 30;

        private readonly Dictionary<int, List<BlockWidgetData>> pages = new();
        private readonly ISubject<BlockWidget> onClicked = new Subject<BlockWidget>();
        private readonly ISubject<BlockWidget> onGrabbed = new Subject<BlockWidget>();
        private readonly IAsyncReactiveProperty<bool> isMenuShowed = new AsyncReactiveProperty<bool>(false);

        private int currentPage;
        private bool useBlockCountText;
        private bool isFirstFrameShowed;
        private Vector3 cachedPosition;
        private IObjectResolver objectResolver;
        private ComponentPool<BlockWidget> blockWidgetPool;
        private ComponentPool<BlockWidgetSocket> blockWidgetSocketPool;
        private CancellationTokenSource blockWidgetCancellationTokenSource;
        private CancellationTokenSource hideMenuCancellationTokenSource;

        private int PageCount => pages.Count;
        private int CurrentPage
        {
            get => currentPage;
            set => currentPage = Mathf.Clamp(value, 0, PageCount);
        }

        public IReadOnlyAsyncReactiveProperty<bool> IsMenuShowed => isMenuShowed;
        public IObservable<BlockWidget> OnClicked => onClicked;
        public IObservable<BlockWidget> OnGrabbed => onGrabbed;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        private void Awake()
        {
            blockWidgetSocketPool = new ComponentPool<BlockWidgetSocket>(blockWidgetSocketPrefab, root, objectResolver);
            blockWidgetPool = new ComponentPool<BlockWidget>(blockWidgetPrefab, root, objectResolver);

            InitializePageWidgets();
        }

        private void OnDestroy()
        {
            blockWidgetCancellationTokenSource.CancelAndDispose();
            StopHideMenuTimer();
        }

        public void SetPose(Vector3 bodyPosition, Quaternion bodyRotation, Vector3 headPosition, Quaternion headRotation)
        {
            if (!IsMenuShowed.Value)
            {
                return;
            }

            transform.position = bodyPosition;

            var scale = root.localScale.x;
            var matrix = Matrix4x4.TRS(bodyPosition, bodyRotation, Vector3.one).inverse;
            var localHeadPosition = matrix.MultiplyPoint3x4(headPosition);
            var localHeadRotation = Quaternion.Euler(0, headRotation.eulerAngles.y, 0);
            var targetPosition = localHeadPosition + 0.5f * scale * (localHeadRotation * (Vector3.forward + 0.3f * Vector3.down));
            var currentRotation = Quaternion.LookRotation(targetPosition.OnlyXZ());

            if (isFirstFrameShowed)
            {
                root.localPosition = cachedPosition = targetPosition;
                root.localRotation = currentRotation;
                isFirstFrameShowed = false;
            }

            if (Quaternion.Angle(currentRotation, root.localRotation) > angleLimit)
            {
                cachedPosition = targetPosition;
            }
            else
            {
                targetPosition = cachedPosition;
            }

            var targetRotation = Quaternion.LookRotation(cachedPosition.OnlyXZ());
            root.localPosition = Vector3.Lerp(root.localPosition, targetPosition, translationSpeed * Time.deltaTime);
            root.localRotation = Quaternion.Slerp(root.localRotation, targetRotation, rotationSpeed * Time.deltaTime);
        }

        public void SetScale(float scale)
        {
            root.localScale = scale * Vector3.one;
        }

        public void ShowMenu(List<BlockWidgetData> blockWidgetDataList, bool useBlockCountText)
        {
            if (blockWidgetDataList.IsNullOrEmpty())
            {
                return;
            }

            isFirstFrameShowed = true;
            isMenuShowed.Value = true;
            this.useBlockCountText = useBlockCountText;

            InitializePages(blockWidgetDataList);
            RenderCurrentPage();
        }

        public void HideMenu()
        {
            Clear();
            StopHideMenuTimer();
            isMenuShowed.Value = false;
        }

        private void InitializePages(List<BlockWidgetData> blockWidgetDataList)
        {
            pages.Clear();

            var page = 0;
            var index = 0;

            while (index < blockWidgetDataList.Count)
            {
                if (!pages.ContainsKey(page))
                {
                    pages[page] = new List<BlockWidgetData>();
                }

                var thisPage = pages[page];
                var isFirstBlockOnPage = thisPage.Count == 0;
                var isLastBlockOnPage = thisPage.Count == BlocksOnPage - 1;
                var isFirstBlock = index == 0;
                var isLastBlock = index == blockWidgetDataList.Count - 1;

                if (isFirstBlockOnPage && !isFirstBlock)
                {
                    thisPage.Add(new BlockWidgetData
                    {
                        id = BackPageKey
                    });
                }
                else if (isLastBlockOnPage && !isLastBlock)
                {
                    thisPage.Add(new BlockWidgetData
                    {
                        id = NextPageKey
                    });
                }
                else
                {
                    thisPage.Add(blockWidgetDataList[index]);
                    index++;
                }

                if (thisPage.Count >= BlocksOnPage)
                {
                    page++;
                }
            }
        }

        private void RenderCurrentPage()
        {
            Clear();

            if (!pages.TryGetValue(CurrentPage, out var blockWidgetDataList))
            {
                return;
            }

            blockWidgetCancellationTokenSource = new CancellationTokenSource();

            var count = blockWidgetDataList.Count;
            var columnCount = count / MaxColumnCount > 0 ? MaxColumnCount : count % MaxColumnCount;
            var rowCount = Mathf.Min(count / columnCount + (count % columnCount == 0 ? 0 : 1), MaxRowCount);
            var columnOrigin = columnCount % 2 == 0 ? -(0.5f * ColumnOffset + ColumnOffset * ((columnCount - 1) / 2)) : -(ColumnOffset * (columnCount / 2));
            var rowOrigin = rowCount % 2 == 0 ? 0.5f * RowOffset + RowOffset * ((rowCount - 1) / 2) : RowOffset * (rowCount / 2);
            var widgetCount = Mathf.Min(count, rowCount * columnCount);
            var point = 0.5f * Vector3.forward;

            for (var i = 0; i < widgetCount; i++)
            {
                var column = i % columnCount;
                var row = i / columnCount;
                var position = Quaternion.Euler(row * RowOffset - rowOrigin, column * ColumnOffset + columnOrigin, 0) * point - point;
                var data = blockWidgetDataList[i];
                var socket = blockWidgetSocketPool.Get();
                socket.SetLocalPosition(position);

                if (data.id == NextPageKey)
                {
                    AddNextPageWidget(socket.Container);
                }
                else if (data.id == BackPageKey)
                {
                    AddBackPageWidget(socket.Container);
                }
                else
                {
                    AddBlockWidget(data, socket.Container);
                }
            }

            StartHideMenuTimer();
        }

        private void RenderPage(int pageDelta)
        {
            if (!IsMenuShowed.Value)
            {
                return;
            }

            CurrentPage += pageDelta;
            RenderCurrentPage();
        }

        private void AddBlockWidget(BlockWidgetData data, Transform parent)
        {
            var widget = blockWidgetPool.Get();
            widget.BubbleDiameter = BubbleDiameter;
            widget.SetParent(parent);
            widget.SetBlock(data, useBlockCountText);
            widget.OnClicked.Subscribe(HandleClickBlockWidget).AddTo(blockWidgetCancellationTokenSource.Token);
            widget.OnGrabbed.Subscribe(HandleDragBlockWidget).AddTo(blockWidgetCancellationTokenSource.Token);
        }

        private void HidePageWidgets()
        {
            nextPageWidget.SetActive(false);
            backPageWidget.SetActive(false);
        }

        private void InitializePageWidgets()
        {
            nextPageWidget.OnClicked.Subscribe(_ => RenderPage(1)).AddTo(nextPageWidget);
            nextPageWidget.OnGrabbed.Subscribe(_ => RenderPage(1)).AddTo(nextPageWidget);
            backPageWidget.OnClicked.Subscribe(_ => RenderPage(-1)).AddTo(backPageWidget);
            backPageWidget.OnGrabbed.Subscribe(_ => RenderPage(-1)).AddTo(backPageWidget);
            backPageWidget.BubbleDiameter = backPageWidget.BubbleDiameter = BubbleDiameter;

            HidePageWidgets();
        }

        private void AddNextPageWidget(Transform parent)
        {
            nextPageWidget.SetParent(parent);
            nextPageWidget.SetActive(true);
        }

        private void AddBackPageWidget(Transform parent)
        {
            backPageWidget.SetParent(parent);
            backPageWidget.SetActive(true);
        }

        private void Clear()
        {
            blockWidgetCancellationTokenSource.CancelAndDispose();
            blockWidgetPool.ReleaseAll(w => w.ClearBlock());
            blockWidgetSocketPool.ReleaseAll();
            HidePageWidgets();
        }

        private void StartHideMenuTimer()
        {
            StopHideMenuTimer();
            hideMenuCancellationTokenSource = new CancellationTokenSource();
            UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(Timeout)).Subscribe(_ => HideMenu()).AddTo(hideMenuCancellationTokenSource.Token);
        }

        private void StopHideMenuTimer()
        {
            hideMenuCancellationTokenSource.CancelAndDispose();
        }

        private void HandleDragBlockWidget(BlockWidget widget)
        {
            onGrabbed.OnNext(widget);
        }

        private void HandleClickBlockWidget(BlockWidget widget)
        {
            onClicked.OnNext(widget);
        }
    }
}