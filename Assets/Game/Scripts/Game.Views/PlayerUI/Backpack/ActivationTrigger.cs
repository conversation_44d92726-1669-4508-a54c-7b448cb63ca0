using System;
using System.Reactive.Subjects;
using Game.Views.Items;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.PlayerUI.Backpack
{
    public class ActivationTrigger : XRSimpleSocketInteractor
    {
        private readonly ISubject<IXRInteractor> onHoverStarted = new Subject<IXRInteractor>();
        private readonly ISubject<IXRInteractor> onHoverEnded = new Subject<IXRInteractor>();

        public BackpackActor HoveredBackpack { get; private set; }
        public IObservable<IXRInteractor> OnHoverStarted => onHoverStarted;
        public IObservable<IXRInteractor> OnHoverEnded => onHoverEnded;

        protected override void OnDisable()
        {
            base.OnDisable();
            UnhoverAll();
            HoveredBackpack = null;
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        public override bool CanSelect(IXRSelectInteractable interactable)
        {
            return false;
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            if (!TryGetBackpack(args.interactableObject, out var backpack) || !backpack.HasStateAuthority || !backpack.IsGrabbed)
            {
                return;
            }

            HoveredBackpack = backpack;
            onHoverStarted.OnNext(backpack.InteractorSelecting);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            if (HoveredBackpack == null)
            {
                return;
            }

            HoveredBackpack = null;
            onHoverEnded.OnNext(args.interactorObject);
        }

        private static bool TryGetBackpack(IXRInteractable interactable, out BackpackActor backpack)
        {
            return interactable.transform.TryGetComponent(out backpack);
        }

        private void UnhoverAll()
        {
            for (var i = interactablesHovered.Count - 1; i >= 0; i--)
            {
                interactionManager.HoverCancel(this, interactablesHovered[i]);
            }
        }
    }
}