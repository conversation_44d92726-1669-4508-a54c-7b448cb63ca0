using System;
using System.Reactive.Subjects;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.PlayerUI.Backpack
{
    public class DeactivationTrigger : XRSimpleInteractable
    {
        private readonly ISubject<IXRInteractor> onHoverStarted = new Subject<IXRInteractor>();
        private readonly ISubject<IXRInteractor> onHoverEnded = new Subject<IXRInteractor>();
        private readonly ISubject<IXRSelectInteractor> onTriggered = new Subject<IXRSelectInteractor>();

        public IObservable<IXRInteractor> OnHoverStarted => onHoverStarted;
        public IObservable<IXRInteractor> OnHoverEnded => onHoverEnded;
        public IObservable<IXRSelectInteractor> OnTriggered => onTriggered;

        protected override void OnDisable()
        {
            base.OnDisable();
            UnhoverAll();
            UnselectAll();
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            if (args.interactorObject is IXRSelectInteractor { hasSelection: false } interactor && !isSelected)
            {
                onHoverStarted.OnNext(interactor);
            }
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            if (args.interactorObject is IXRSelectInteractor interactor && (DeactivationTrigger)args.interactableObject == this)
            {
                onHoverEnded.OnNext(interactor);

                if (interactor.hasSelection && isSelected)
                {
                    onTriggered.OnNext(interactor);
                }
            }
        }

        private void UnhoverAll()
        {
            for (var i = interactorsHovering.Count - 1; i >= 0; i--)
            {
                interactionManager.HoverCancel(interactorsHovering[i], this);
            }
        }

        private void UnselectAll()
        {
            for (var i = interactorsSelecting.Count - 1; i >= 0; i--)
            {
                interactionManager.SelectCancel(interactorsSelecting[i], this);
            }
        }
    }
}