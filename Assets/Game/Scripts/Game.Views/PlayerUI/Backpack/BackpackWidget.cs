using DG.Tweening;
using Game.Views.Interactables;
using Game.Views.Items;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.PlayerUI.Backpack
{
    public class BackpackWidget : Actor
    {
        [SerializeField] private Transform viewNode;
        [SerializeField] private Material previewMaterial;

        private Sequence sequence;
        private BackpackView backpackView;
        private InteractablesManager interactablesManager;

        [Inject]
        private void Construct(InteractablesManager interactablesManager)
        {
            this.interactablesManager = interactablesManager;
        }

        private void OnDisable()
        {
            sequence?.Kill();
            transform.localScale = Vector3.one;
        }

        private void OnDestroy()
        {
            DestroyView();
        }

        public override void SetActive(bool isActive)
        {
            if (!isActive)
            {
                DestroyView();
            }

            base.SetActive(isActive);
        }

        public void Render(string viewCode)
        {
            CreateView(viewCode);
            Show();
        }

        public void SetDefaultView()
        {
            if (backpackView == null)
            {
                return;
            }

            backpackView.RevertCustomMaterial();
        }

        public void SetGhostView()
        {
            if (backpackView == null)
            {
                return;
            }

            backpackView.ApplyCustomMaterial(previewMaterial);
        }

        public void SetHoverStartState()
        {
            sequence?.Kill();
            sequence = DOTween.Sequence();
            sequence.Append(transform.DOScale(1.5f * Vector3.one, 0.25f));
        }

        public void SetHoverEndState()
        {
            sequence?.Kill();
            sequence = DOTween.Sequence();
            sequence.Append(transform.DOScale(Vector3.one, 0.25f));
        }

        private void CreateView(string code)
        {
            DestroyView();

            var view = interactablesManager.CreateView(code, viewNode);
            if (view == null)
            {
                return;
            }

            if (view is BackpackView backpackView)
            {
                this.backpackView = backpackView;
                backpackView.SetPreviewState(0.1f);
            }
            else
            {
                interactablesManager.DestroyView(view);
            }
        }

        private void DestroyView()
        {
            if (backpackView == null)
            {
                return;
            }

            backpackView.RevertCustomMaterial();
            interactablesManager.DestroyView(backpackView);
            backpackView = null;
        }
    }
}