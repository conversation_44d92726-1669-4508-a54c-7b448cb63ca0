using DG.Tweening;
using Modules.Core;
using TMPro;
using UnityEngine;

namespace Game.Views.PlayerUI.Wrist
{
    public class WristMenu : Actor
    {
        [SerializeField] private GameObject rootObject;
        [SerializeField] private TMP_Text playerNameText;
        [SerializeField] private TMP_Text playerHpText;
        [SerializeField] private TMP_Text coinAmountText;
        [SerializeField] private TMP_Text diamondText;
        [SerializeField] private Vector3 translationOffset;
        [SerializeField] private Vector3 rotationOffset;

        private int currentCoinAmount = -1;
        private int currentDiamondAmount = -1;

        public void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        public void UpdatePose(Vector3 position, Quaternion rotation)
        {
            position += rotation * translationOffset;
            rotation *= Quaternion.Euler(rotationOffset);
            transform.SetPositionAndRotation(position, rotation);
        }

        public void SetPlayerName(string playerName)
        {
            playerNameText.text = $"{playerName}";
        }

        public void SetPlayerHp(int hp)
        {
            playerHpText.text = $"{hp} <sprite name=Heart>";
        }

        public void SetCoinAmount(int coinAmount)
        {
            if (currentCoinAmount != -1)
            {
                DOVirtual.Int(currentCoinAmount, coinAmount, 0.3f, i => coinAmountText.text = $"{i} <sprite name=Coin>");
            }

            coinAmountText.text = $"{coinAmount} <sprite name=Coin>";
            currentCoinAmount = coinAmount;
        }

        public void SetDiamondAmount(int diamondAmount)
        {
            if (currentDiamondAmount != -1)
            {
                DOVirtual.Int(currentDiamondAmount, diamondAmount, 0.3f, i => diamondText.text = $"{i} <sprite name=Diamond>");
            }

            diamondText.text = $"{diamondAmount} <sprite name=Diamond>";
            currentDiamondAmount = diamondAmount;
        }

        public void SetActivePlayerHp(bool isActive)
        {
            playerHpText.gameObject.SetActive(isActive);
        }
    }
}