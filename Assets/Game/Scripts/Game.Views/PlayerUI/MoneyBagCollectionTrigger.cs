using System;
using System.Reactive.Subjects;
using Game.Views.Items;
using Modules.XR;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.PlayerUI
{
    public class MoneyBagCollectionTrigger : XRSimpleSocketInteractor
    {
        private readonly ISubject<MoneyBagActor> onBagCollected = new Subject<MoneyBagActor>();
        private readonly ISubject<IXRInteractor> onHoverStarted = new Subject<IXRInteractor>();
        private readonly ISubject<IXRInteractor> onHoverEnded = new Subject<IXRInteractor>();

        private bool isHover;

        public IObservable<IXRInteractor> OnHoverStarted => onHoverStarted;
        public IObservable<IXRInteractor> OnHoverEnded => onHoverEnded;
        public IObservable<MoneyBagActor> OnBagCollected => onBagCollected;

        public override bool CanSelect(IXRSelectInteractable interactable)
        {
            return base.CanSelect(interactable) && !interactable.isSelected && isHover;
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            isHover = false;
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);

            if (!TryGetMoneyBagActor(args.interactableObject, out var actor) || !actor.HasStateAuthority)
            {
                return;
            }

            isHover = actor.IsGrabbed;

            if (isHover)
            {
                onHoverStarted.OnNext(actor.InteractorSelecting);
            }
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);

            if (!isHover)
            {
                return;
            }

            onHoverEnded.OnNext(args.interactorObject);
            isHover = false;
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);

            if (isHover && TryGetMoneyBagActor(args.interactableObject, out var actor) && actor.HasStateAuthority)
            {
                onBagCollected.OnNext(actor);
            }
        }

        private static bool TryGetMoneyBagActor(IXRInteractable interactable, out MoneyBagActor actor)
        {
            return interactable.transform.TryGetComponent(out actor);
        }
    }
}