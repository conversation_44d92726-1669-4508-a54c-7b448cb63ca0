using Fusion;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        [Networked] [OnChangedRender(nameof(ChangeBadgeId))]
        public byte BadgeId { get; private set; }

        public void SetBadge(string code)
        {
            BadgeId = (byte)(badgesConfig.TryGetId(code, out var id) ? id : 0);
        }

        public void SetBadge(int id)
        {
            BadgeId = (byte)id;
        }

        private void ChangeBadgeId()
        {
            badgesManager.DestroyView(badgeView);

            if (avatarProvider.AvatarView.BadgeNode != null && badgesManager.TryCreateView(BadgeId, avatarProvider.AvatarView.BadgeNode, out var view))
            {
                badgeView = view;
            }
        }

        private void DestroyBadge()
        {
            badgesManager.DestroyView(badgeView);
            badgeView = null;
        }

        private void UpdateBadgeOffset(bool useOffset)
        {
            if (avatarProvider.AvatarView.BadgeNode != null)
            {
                var node = avatarProvider.AvatarView.BadgeNode;
                node.localPosition = useOffset ? node.localPosition.SetZ(0.03f) : Vector3.zero;
            }
        }
    }
}