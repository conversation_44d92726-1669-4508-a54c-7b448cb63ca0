using System;
using System.Reactive.Subjects;
using Fusion;
using Game.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly ISubject<PlayerDamageOnPlayerArgs> onPlayerDamageReceived = new Subject<PlayerDamageOnPlayerArgs>();
        private readonly ISubject<NPCDamageOnPlayerArgs> onZombieDamageReceived = new Subject<NPCDamageOnPlayerArgs>();
        private readonly ISubject<LavaDamageOnPlayerArgs> onLavaDamageReceived = new Subject<LavaDamageOnPlayerArgs>();

        public IObservable<PlayerDamageOnPlayerArgs> OnPlayerDamageReceived => onPlayerDamageReceived;
        public IObservable<NPCDamageOnPlayerArgs> OnZombieDamageReceived => onZombieDamageReceived;
        public IObservable<LavaDamageOnPlayerArgs> OnLavaDamageReceived => onLavaDamageReceived;

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByPlayerRpc(byte damage, Vector3 force, RpcInfo info = default)
        {
            if (IsDead)
            {
                return;
            }

            onPlayerDamageReceived.OnNext(new PlayerDamageOnPlayerArgs(info.Source, damage, force));
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByZombieRpc(byte damage)
        {
            if (IsDead)
            {
                return;
            }

            audioClient.Play(AudioKeys.ZombieAttack, HeadNode, destroyCancellationToken);
            onZombieDamageReceived.OnNext(new NPCDamageOnPlayerArgs(damage));
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority, Channel = RpcChannel.Unreliable)]
        public void SetDamageByMonsterRpc(byte damage)
        {
            if (IsDead)
            {
                return;
            }

            onZombieDamageReceived.OnNext(new NPCDamageOnPlayerArgs(damage));
        }

        public void SetDamageByLava(byte damage)
        {
            if (!HasStateAuthority || IsDead)
            {
                return;
            }

            audioClient.Play(AudioKeys.LavaBurn, HeadNode, destroyCancellationToken);
            onLavaDamageReceived.OnNext(new LavaDamageOnPlayerArgs(damage));
        }
    }
}