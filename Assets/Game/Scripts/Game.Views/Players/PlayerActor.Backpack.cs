using Fusion;
using Game.Views.Items;
using Modules.Core;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        [Networked] [OnChangedRender(nameof(ChangeBackpack))]
        public BackpackActor Backpack { get; private set; }

        public void EquipBackpack(BackpackActor backpack)
        {
            if (Backpack != null || backpack == null)
            {
                return;
            }

            Backpack = backpack;
            Backpack.IsEquipped = true;
        }

        public void UnequipBackpack()
        {
            if (Backpack == null)
            {
                return;
            }

            Backpack.IsEquipped = false;
            Backpack = null;
        }

        private void RenderRemoteBackpackPose()
        {
            if (HasStateAuthority || Backpack == null || avatarProvider.AvatarView.BackpackNode == null)
            {
                return;
            }

            var pose = avatarProvider.AvatarView.BackpackNode.GetPose();
            Backpack.transform.SetPose(pose);
        }

        private void RenderLocalBackpackPose()
        {
            if (Backpack == null || avatarProvider.AvatarView.BackpackNode == null)
            {
                return;
            }

            var pose = avatarProvider.AvatarView.BackpackNode.GetPose();
            Backpack.transform.SetPose(pose);
        }

        private void ChangeBackpack()
        {
        }
    }
}