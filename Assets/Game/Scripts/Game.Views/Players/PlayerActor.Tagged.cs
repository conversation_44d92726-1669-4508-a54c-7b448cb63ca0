using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<bool> isTagged = new AsyncReactiveProperty<bool>(false);

        public IReadOnlyAsyncReactiveProperty<bool> IsTagged => isTagged;

        [Networked] [OnChangedRender(nameof(ChangeIsTaggedNetworked))]
        private NetworkBool IsTaggedNetworked { get; set; }

        public void SetTagged(bool isInfected)
        {
            SendRpcSafe(() => SetTaggedRpc(isInfected));
        }

        public void SetInfected(bool isInfected)
        {
            if (isInfected)
            {
                avatarProvider.ApplyCustomMaterial(playersConfig.InfectionMaterial);
            }
            else
            {
                avatarProvider.RevertCustomMaterial();
            }
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private void SetTaggedRpc(bool isInfected)
        {
            audioClient.Play(isInfected ? AudioKeys.Infected : AudioKeys.ClearInfection, transform, destroyCancellationToken);
            IsTaggedNetworked = isInfected;
        }

        private void ChangeIsTaggedNetworked()
        {
            if (isTagged.Value != IsTaggedNetworked)
            {
                isTagged.Value = IsTaggedNetworked;
            }
        }
    }
}