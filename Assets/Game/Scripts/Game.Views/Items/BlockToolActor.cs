using System;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Game.Views.PlayerUI.Blocks;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.Items
{
    public enum BlockToolMode
    {
        Ray = 0,
        Gun = 1
    }

    public class BlockToolActor : ItemActor<BlockToolView>
    {
        [Header("BlockTool")]
        [SerializeField] private BlockToolMode blockToolMode;
        [SerializeField] private BlockWidget blockWidget;
        [SerializeField] private Transform blockWidgetParent;

        private readonly ISubject<BlockToolActor> onBlockClicked = new Subject<BlockToolActor>();
        private readonly ISubject<BlockToolActor> onFired = new Subject<BlockToolActor>();

        public int BlockId { get; private set; }
        public Transform BarrelNode => HasView ? View.BarrelNode : transform;
        public XRDirectInteractor Interactor { get; private set; }
        public BlockToolMode BlockToolMode => blockToolMode;
        public IObservable<BlockToolActor> OnBlockClicked => onBlockClicked;
        public IObservable<BlockToolActor> OnFired => onFired;

        public override void Spawned()
        {
            base.Spawned();
            ClearBlock();
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            Interactor = args.interactorObject.IsLeftHand() ? XRInput.LeftDirectInteractor : XRInput.RightDirectInteractor;
            var onFire = args.interactorObject.IsLeftHand() ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onFire.Where(_ => HasStateAuthority && HasView).Where(obj => obj.performed).Subscribe(_ => HandleFired()).AddTo(DropCancellationToken);
            blockWidget.OnGrabbed.Subscribe(HandleBlockWidget).AddTo(DropCancellationToken);
            blockWidget.OnClicked.Subscribe(HandleBlockWidget).AddTo(DropCancellationToken);
            base.HandleSelectEntered(args);
        }

        protected override void HandleSelectExited(SelectExitEventArgs args)
        {
            base.HandleSelectExited(args);
            ClearBlock();
            Interactor = null;
        }

        private void HandleBlockWidget(BlockWidget widget)
        {
            onBlockClicked.OnNext(this);
        }

        public void SetBlock(BlockWidgetData blockWidgetData, bool useBlockCountText)
        {
            BlockId = blockWidgetData.id;
            blockWidget.SetBlock(blockWidgetData, useBlockCountText);
            blockWidget.SetActive(true);
        }

        public void ClearBlock()
        {
            BlockId = -1;
            blockWidget.ClearBlock();
            blockWidget.SetActive(false);
        }

        private void HandleFired()
        {
            onFired.OnNext(this);
        }

        public void SetBlockCount(int blockCount)
        {
            blockWidget.SetBlockCount(blockCount);
        }
    }
}