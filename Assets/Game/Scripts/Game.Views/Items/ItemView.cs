using System.Collections.Generic;
using Game.Core.Extensions;
using Game.Views.InteractablesCore;
using UnityEngine;

namespace Game.Views.Items
{
    public abstract class ItemView : InteractableView
    {
        [SerializeField] private Transform collidersNode;

        private Material sourceMaterial;
        private float previewScaleMultiplier;
        private readonly List<Renderer> rendererList = new();

        protected virtual void Awake()
        {
            transform.GetComponentsInChildren(true, rendererList);
            collidersNode.GetComponentsInChildren(true, AllColliderList);
            GameUtility.IgnoreCollisions(AllColliderList);
            previewScaleMultiplier = GetPreviewScaleMultiplier();
            sourceMaterial = rendererList.Capacity > 0 ? rendererList[0].sharedMaterial : null;
        }

        public override void ApplyCustomMaterial(Material customMaterial)
        {
            base.ApplyCustomMaterial(customMaterial);
            rendererList.ForEach(r => r.sharedMaterial = customMaterial);
        }

        public override void RevertCustomMaterial()
        {
            base.RevertCustomMaterial();
            rendererList.ForEach(r => r.sharedMaterial = sourceMaterial);
        }

        public override void SetPreviewState(float scale = 1)
        {
            base.SetPreviewState(scale * previewScaleMultiplier);
        }

        protected virtual float GetPreviewScaleMultiplier()
        {
            var value = GetBounds(rendererList).size.magnitude;
            return value == 0 ? 1 : 1 / value;
        }
    }
}