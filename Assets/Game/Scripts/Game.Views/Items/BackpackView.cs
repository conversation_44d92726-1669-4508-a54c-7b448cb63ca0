using DG.Tweening;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Items
{
    public class BackpackView : ItemView
    {
        [SerializeField] private Transform pocketNode;
        [SerializeField] private Transform topNode;
        [SerializeField] private float openedAngle;

        public Pose PocketPose => pocketNode.GetPose();

        public void SetOpened(bool isOpened, bool withAnimation)
        {
            var angle = isOpened ? openedAngle : 0;
            topNode.DOLocalRotate(new Vector3(angle, 0, 0), withAnimation ? 0.1f : 0).SetEase(Ease.OutCubic);
        }
    }
}