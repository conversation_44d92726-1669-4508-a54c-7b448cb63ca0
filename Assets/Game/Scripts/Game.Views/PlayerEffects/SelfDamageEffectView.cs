using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using DG.Tweening;
using Modules.Core;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Views.PlayerEffects
{
    public class SelfDamageEffectView : Actor
    {
        [SerializeField] private Renderer selfRenderer;
        [SerializeField] private Transform shakeObject;

        private Tweener effectTweener;
        private Sequence shakeSequence;
        private CancellationTokenSource effectCancellationTokenSource;

        private void OnDisable()
        {
            effectTweener?.Kill();
            shakeSequence?.Kill();
            effectCancellationTokenSource.CancelAndDispose();
        }

        public override void SetActive(bool isActive)
        {
            base.SetActive(isActive);
            if (isActive)
            {
                RenderEffect();
            }
        }

        public void Instantiate(float duration = 2, bool shake = false)
        {
            base.SetActive(true);
            RenderEffect(duration, shake);
        }

        private void RenderEffect(float duration = 2, bool shake = false)
        {
            effectTweener?.Kill();
            shakeSequence?.Kill();

            effectTweener = selfRenderer.sharedMaterial.DOFade(1, 0.1f);

            if (shake && shakeObject != null)
            {
                shakeSequence = DOTween.Sequence();
                shakeSequence.Join(shakeObject.DOShakePosition(duration * 2, 0.05f, 20, fadeOut: false));
                shakeSequence.Join(shakeObject.DOShakeRotation(duration * 2, 2f, 20, fadeOut: false));
                shakeSequence.Play();
            }

            effectCancellationTokenSource.CancelAndDispose();
            effectCancellationTokenSource = new CancellationTokenSource();
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(1))
                .Subscribe(_ =>
                {
                    effectTweener?.Kill();
                    effectTweener = selfRenderer.sharedMaterial.DOFade(0, duration).OnComplete(Hide);
                })
                .AddTo(effectCancellationTokenSource.Token);
        }

        [Button]
        private void T()
        {
            SetActive(true);
        }
    }
}