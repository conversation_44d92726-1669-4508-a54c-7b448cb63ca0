using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Views.BuildIslands;
using Game.Views.Portals;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.Levels
{
    public partial class LevelSpaceManager : MonoBehaviour, IClosedBuildZone
    {
        [SerializeField] private GameObject root;
        [SerializeField] private LevelPortalView portal;
        [SerializeField] private GameObject minesPosterNode;
        [SerializeField] private Transform mirrorNode;
        [SerializeField] private Transform smallShopNode;
        [SerializeField] private List<Transform> gadgetShopViewNodeList;
        [SerializeField] private List<Transform> oreConverterViewNodeList;

        private readonly ISubject<PortalView> onPortalTriggered = new Subject<PortalView>();

        private BuildZoneHandler buildZoneHandler;
        private CancellationTokenSource disableCancellationTokenSource;

        public LevelPortalView Portal => portal;
        public GameObject MinesPosterNode => minesPosterNode;
        public Transform MirrorNode => mirrorNode;
        public Transform SmallShopNode => smallShopNode;
        public List<Transform> GadgetShopViewNodeList => gadgetShopViewNodeList;
        public List<Transform> OreConverterViewNodeList => oreConverterViewNodeList;
        public IObservable<PortalView> OnPortalTriggered => onPortalTriggered;

        [Inject]
        private void Construct(IObjectResolver objectResolver, INetworkClient networkClient, BuildZoneHandler buildZoneHandler)
        {
            this.buildZoneHandler = buildZoneHandler;

            objectResolver.InjectGameObject(root);

            networkClient.OnNetworkActorSpawned.Subscribe(x => HandleNetworkActorSpawned(x.actor)).AddTo(destroyCancellationToken);
            networkClient.OnShutdown.Subscribe(_ => HandleShutdown()).AddTo(destroyCancellationToken);
        }

        private void OnEnable()
        {
            disableCancellationTokenSource = new CancellationTokenSource();
            portal.OnPlayerTriggered.Subscribe(onPortalTriggered.OnNext).AddTo(disableCancellationTokenSource.Token);
            buildZoneHandler.RegisterClosedBuildZone(this);
        }

        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            buildZoneHandler.UnregisterClosedBuildZone(this);
        }

        public void SetActiveLevel(bool isActive)
        {
            root.SetActive(isActive);

            if (isActive)
            {
                portal.SetActiveTrigger(true);
            }
        }

        public Pose GetPortalSpawnPose()
        {
            return portal.GetSpawnPose();
        }

        public bool IsClosedBuildZone(Vector3 position)
        {
            return portal.IsClosedBuildZone(position);
        }

        private void HandleNetworkActorSpawned(NetworkActor actor)
        {
            TryAddLevelPortalMasterClientObject(actor);
        }

        private void HandleShutdown()
        {
            RemoveLevelPortalMasterClientObject();
        }
    }
}