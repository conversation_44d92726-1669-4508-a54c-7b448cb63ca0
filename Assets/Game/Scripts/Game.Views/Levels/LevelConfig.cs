using System;
using Game.Core.Data;
using UnityEngine;

namespace Game.Views.Levels
{
    [Serializable]
    public class LevelConfig
    {
        [SerializeField] private GameMode gameMode;
        [SerializeField] private bool useWeapons;
        [SerializeField] private bool useGuns;
        [SerializeField] private bool useWings;
        [SerializeField] private bool useBuildZones;
        [SerializeField] private bool useZombies;
        [SerializeField] private bool useMonsters;
        [SerializeField] private bool useWeaponsDamageOnPlayer;
        [SerializeField] private bool useGunsDamageOnPlayer;
        [SerializeField] private bool useLavaDamageOnPlayer;
        [SerializeField] private bool isCreateVoxels;
        [SerializeField] private bool isDestroyVoxels;
        [SerializeField] private bool isInfectedWhenDead;
        [SerializeField] private bool isInfectedWhenTouched;
        [SerializeField] private ConsumablesConfig consumables;
        [SerializeField] private TimeOfDayConfig timeOfDay;

        [Header("Mining")]
        [SerializeField] private MiningConfig miningConfig;

        [Header("In-Game Menus")]
        [SerializeField] private bool disableHomeButton;
        [SerializeField] private bool disableMoneyBagSpawner;
        [SerializeField] private bool disableArmInteractableWidgets;

        public GameMode GameMode => gameMode;
        public bool UseWeapons => useWeapons;
        public bool UseGuns => useGuns;
        public bool UseWings => useWings;
        public bool UseBuildZones => useBuildZones;
        public bool UseZombies => useZombies;
        public bool UseMonsters => useMonsters;
        public bool UseWeaponsDamageOnPlayer => useWeaponsDamageOnPlayer;
        public bool UseGunsDamageOnPlayer => useGunsDamageOnPlayer;
        public bool UseLavaDamageOnPlayer => useLavaDamageOnPlayer;
        public bool UseAnyDamageOnPlayer => useWeaponsDamageOnPlayer || useGunsDamageOnPlayer || useLavaDamageOnPlayer;
        public bool IsCreateVoxels => isCreateVoxels;
        public bool IsDestroyVoxels => isDestroyVoxels;
        public bool IsInfectedWhenDead => isInfectedWhenDead;
        public bool IsInfectedWhenTouched => isInfectedWhenTouched;
        public ConsumablesConfig Consumables => consumables;
        public TimeOfDayConfig TimeOfDay => timeOfDay;
        public bool DisableHomeButton => disableHomeButton;
        public bool DisableMoneyBagSpawner => disableMoneyBagSpawner;
        public bool DisableArmInteractableWidgets => disableArmInteractableWidgets;
        public MiningConfig MiningSettings => miningConfig;

        [Serializable]
        public class ConsumablesConfig
        {
            public int updateInterval = 15;
            public bool considerBounds;
            public Vector3 boundsOrigin;
            public Vector3 boundsSize;

            [Header("Ammo")]
            public bool useAmmo;
            public int maxAmmoCount = 5;

            [Header("Medical")]
            public bool useMedical;
            public int maxMedicalCount = 5;
        }

        [Serializable]
        public class TimeOfDayConfig
        {
            public bool useTimeOfDay;
            public float offset = 12;
            public float speed = 0.01f;
        }

        [Serializable]
        public class MiningConfig
        {
            public bool useOreConverter;
            public Vector4[] positions;
        }
    }
}