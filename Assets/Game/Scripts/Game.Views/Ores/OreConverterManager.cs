using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Ores
{
    public class OreConverterManager : Actor
    {
        [SerializeField] private OreConverterView oreConverterViewPrefab;

        private IObjectResolver objectResolver;
        private ComponentPool<OreConverterView> oreConverterViewPool;
        private CancellationTokenSource oreConverterViewCancellationTokenSource;

        private readonly ISubject<InteractableActor> onInteractableEntered = new Subject<InteractableActor>();
        private readonly ISubject<BackpackActor> onBackpackEntered = new Subject<BackpackActor>();

        public IObservable<InteractableActor> OnInteractableEntered => onInteractableEntered;
        public IObservable<BackpackActor> OnBackpackEntered => onBackpackEntered;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        private void Awake()
        {
            oreConverterViewPool = new ComponentPool<OreConverterView>(oreConverterViewPrefab, transform, objectResolver, 0);
        }

        public void ClearInstances()
        {
            oreConverterViewCancellationTokenSource.CancelAndDispose();
            oreConverterViewCancellationTokenSource = null;
            oreConverterViewPool.ReleaseAll();
        }

        public void CreateInstance(Pose pose)
        {
            oreConverterViewCancellationTokenSource ??= new CancellationTokenSource();

            var view = oreConverterViewPool.Get();
            view.SetPose(pose);
            view.OnInteractableEntered.Subscribe(HandleInteractableEntered).AddTo(oreConverterViewCancellationTokenSource.Token);
            view.OnBackpackEntered.Subscribe(HandleBackpackEntered).AddTo(oreConverterViewCancellationTokenSource.Token);
        }

        private void HandleInteractableEntered(InteractableActor interactable)
        {
            onInteractableEntered.OnNext(interactable);
        }

        private void HandleBackpackEntered(BackpackActor backpack)
        {
            onBackpackEntered.OnNext(backpack);
        }
    }
}