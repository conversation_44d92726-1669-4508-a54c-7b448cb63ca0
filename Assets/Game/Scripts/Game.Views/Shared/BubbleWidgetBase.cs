using System;
using System.Reactive.Subjects;
using DG.Tweening;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using XRSimpleInteractable = Modules.XR.XRSimpleInteractable;

namespace Game.Views.Shared
{
    public abstract class BubbleWidgetBase<T> : XRSimpleInteractable
    {
        private const float ClickInterval = 0.2f;

        [Header("BubbleWidget")]
        [SerializeField] private Renderer bubbleRenderer;
        [SerializeField] private Transform bubbleNode;
        [SerializeField] private Transform viewNode;
        [SerializeField] private float hoverScale = 1.5f;
        [SerializeField] private float grabEndThreshold = 0.1f;

        protected readonly ISubject<T> onClicked = new Subject<T>();
        protected readonly ISubject<T> onGrabbed = new Subject<T>();

        private IXRInput xrInput;
        private Tweener scaleTweener;
        private float selectExitedClickTime;
        private Vector3 originLocalPosition;
        private Vector3 clickDeltaPosition;
        private Vector3 clickOriginPosition;
        private float initialGrabDistance;

        protected Transform ViewNode => viewNode;
        protected IAudioClient AudioClient { get; private set; }
        protected virtual bool CanCallClickWhenGrabbing { get; set; }

        public float BubbleDiameter
        {
            get => bubbleNode.localScale.x;
            set => bubbleNode.localScale = value * Vector3.one;
        }
        public IXRSelectInteractor GrabInteractor { get; private set; }
        public IObservable<T> OnGrabbed => onGrabbed;
        public IObservable<T> OnClicked => onClicked;

        [Inject]
        private void Construct(IXRInput xrInput, IAudioClient audioClient)
        {
            this.xrInput = xrInput;
            AudioClient = audioClient;
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ResetScale();
        }

        protected virtual void Update()
        {
            UpdateClickAndGrab();
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);

            AnimateScale(hoverScale);
            xrInput.SendHapticImpulse(args.interactorObject, HapticImpulse.ShortDurationLowAmplitude);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);

            AnimateScale(1);
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);

            GrabInteractor = args.interactorObject;
            clickOriginPosition = transform.position;
            clickDeltaPosition = clickOriginPosition - args.interactorObject.transform.position;

            initialGrabDistance = Vector3.Distance(clickOriginPosition, GrabInteractor.transform.position);
            selectExitedClickTime = Time.time + ClickInterval;
        }

        protected override void OnSelectExited(SelectExitEventArgs args)
        {
            base.OnSelectExited(args);

            if (selectExitedClickTime > Time.time)
            {
                CallClicked();
            }
            else
            {
                ResetInputForClick();
            }
        }

        protected override void OnDeactivated(DeactivateEventArgs args)
        {
            base.OnDeactivated(args);

            CallClicked();
        }

        public void SetActive(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        public void SetParent(Transform parent)
        {
            originLocalPosition = Vector3.zero;
            transform.SetParent(parent, false);
        }

        public void SetOriginLocalPositionAndRotation(Vector3 position, Quaternion rotation)
        {
            originLocalPosition = position;
            transform.SetLocalPositionAndRotation(position, rotation);
        }

        protected virtual void CallClicked()
        {
            ResetInputForClick();
        }

        protected virtual void CallGrabbed()
        {
            ResetInputForGrab();
        }

        private void UpdateClickAndGrab()
        {
            if (GrabInteractor == null)
            {
                return;
            }

            var currentDistance = Vector3.Distance(clickOriginPosition, GrabInteractor.transform.position);
            var movedDistance = Mathf.Abs(currentDistance - initialGrabDistance);

            if (movedDistance > grabEndThreshold)
            {
                if (CanCallClickWhenGrabbing)
                {
                    CallClicked();
                }
                else
                {
                    CallGrabbed();
                    GrabInteractor = null;
                }

                DeselectAll();
            }
            else
            {
                transform.position = GrabInteractor.transform.position + clickDeltaPosition;
            }
        }

        private void ResetInputForClick()
        {
            GrabInteractor = null;
            transform.localPosition = originLocalPosition;
            selectExitedClickTime = 0;
        }

        private void ResetInputForGrab()
        {
            transform.localPosition = originLocalPosition;
            selectExitedClickTime = 0;
        }

        public void AnimateScale(float scale)
        {
            scaleTweener?.Kill();
            scaleTweener = transform.DOScale(scale * Vector3.one, 0.3f).SetEase(Ease.OutBack);
        }

        private void ResetScale()
        {
            scaleTweener?.Kill();
            transform.localScale = Vector3.one;
        }

        private void DeselectAll()
        {
            for (var i = interactorsSelecting.Count - 1; i >= 0; i--)
            {
                interactionManager.SelectCancel(interactorsSelecting[i], this);
            }
        }
    }
}