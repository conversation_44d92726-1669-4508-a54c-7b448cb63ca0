using UnityEngine;
using VoxelPlay;

namespace Game.Views.Voxels
{
    public readonly struct DestroyedVoxelArgs
    {
        public readonly Vector3 position;
        public readonly VoxelDefinition voxelDefinition;
        public readonly bool byLocalPlayer;

        public DestroyedVoxelArgs(Vector3 position, VoxelDefinition voxelDefinition, bool byLocalPlayer)
        {
            this.position = position;
            this.voxelDefinition = voxelDefinition;
            this.byLocalPlayer = byLocalPlayer;
        }
    }
}