using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Game.Core.Data;
using Game.View.Shared;
using Game.Views.Portals;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Lobby
{
    public class LobbySpaceManager : MonoBehaviour
    {
        [SerializeField] private GameObject root;
        [SerializeField] private SpawnPoint homeSpawnPoint;
        [SerializeField] private SpawnPoint prisonSpawnPoint;
        [SerializeField] private CustomPortalView customPortal;
        [SerializeField] private List<LobbyPortalView> lobbyPortalList;
        [SerializeField] private List<Transform> itemSpawnPointList;
        [SerializeField] private Transform blockShopNode;
        [SerializeField] private Transform mirrorNode;
        [SerializeField] private Transform smallShopNode;

        private readonly ISubject<PortalView> onPortalTriggered = new Subject<PortalView>();

        public CustomPortalView CustomPortal => customPortal;
        public Transform BlockShopNode => blockShopNode;
        public Transform MirrorNode => mirrorNode;
        public Transform SmallShopNode => smallShopNode;
        public IObservable<PortalView> OnPortalTriggered => onPortalTriggered;

        public Pose GetHomeSpawnPose(float radius = 0)
        {
            return homeSpawnPoint.GetPose(radius);
        }

        public Pose GetPrisonSpawnPose(float radius = 0)
        {
            return prisonSpawnPoint.GetPose(radius);
        }

        public bool TryGetRandomItemSpawnPoint(out Vector3 point)
        {
            if (itemSpawnPointList.Count == 0)
            {
                point = Vector3.zero;
                return false;
            }

            point = itemSpawnPointList.RandomItem().position;
            return true;
        }

        public void SetActiveLobby(bool isActive)
        {
            root.SetActive(isActive);
        }

        public void InitializePortals(List<LobbyPortalData> portalDataList, List<LevelMetaData> levelDataList)
        {
            DeactivatePortals();

            foreach (var portalView in lobbyPortalList)
            {
                var portalData = portalDataList.Find(p => p.type == portalView.PortalType);
                if (portalData == null)
                {
                    portalView.Hide();
                }
                else
                {
                    var levelData = levelDataList.Find(p => p.id == portalData.levelId);
                    if (levelData == null)
                    {
                        portalView.Hide();
                    }
                    else
                    {
                        levelData.name = portalData.title;
                        ActivatePortal(portalView, levelData);
                    }
                }
            }
        }

        private void ActivatePortal(LobbyPortalView portal, LevelMetaData levelData)
        {
            portal.OnPlayerTriggered.Subscribe(onPortalTriggered.OnNext).AddTo(portal.DisableCancellationToken);
            portal.Initialize(levelData);
        }

        private void DeactivatePortals()
        {
            lobbyPortalList.ForEach(x => x.Hide());
        }
    }
}