using Modules.Core;
using TMPro;
using UnityEngine;

namespace Game.Views.Guns
{
    public class AmmoIndicator : Actor
    {
        [SerializeField] private TMP_Text ammoText;

        public void SetAmmo(int ammo)
        {
            ammoText.text = ammo.ToString();
        }

        public void SetHandPosition(bool isLeftHand)
        {
            var xPos = Mathf.Abs(transform.localPosition.x);
            transform.localPosition = transform.localPosition.SetX(isLeftHand ? xPos : -xPos);
        }
    }
}