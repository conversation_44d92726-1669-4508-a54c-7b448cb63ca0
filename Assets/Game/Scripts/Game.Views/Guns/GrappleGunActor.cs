using System;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.View.Locomotions;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.Guns
{
    public class GrappleGunActor : GunActor<GrappleGunView>
    {
        [Header("GrappleGun")]
        [SerializeField] private float maxHookSqrDistance;
        [SerializeField] private float unhookSqrDistance;
        [SerializeField] private float hookSpeed;

        private readonly ISubject<GrappleHookArgs> onHooked = new Subject<GrappleHookArgs>();
        private readonly ISubject<GrappleUnhookArgs> onUnhooked = new Subject<GrappleUnhookArgs>();

        private GrappleGunHookView hookView;
        private bool isHookedLocal;

        private bool HasHook => hookView != null;
        [Networked] private ref NetworkHookData HookData => ref MakeRef<NetworkHookData>();
        public IObservable<GrappleHookArgs> OnHooked => onHooked;
        public IObservable<GrappleUnhookArgs> OnUnhooked => onUnhooked;

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();
            if (!HasStateAuthority)
            {
                return;
            }

            if (HookData.IsActive)
            {
                UpdateActiveState(Runner.Tick);
            }
        }

        public override void Render()
        {
            base.Render();

            if (!HookData.IsActive)
            {
                if (HasHook)
                {
                    DestroyHook();
                }

                isHookedLocal = false;
                return;
            }

            if (!HasHook)
            {
                hookView = GunsManager.CreateHook();
                AudioClient.Play(AudioKeys.GrappleGunFire, View.BarrelNode.position, destroyCancellationToken);
            }

            if (HookData.IsHooked && !isHookedLocal)
            {
                isHookedLocal = true;
                AudioClient.Play(AudioKeys.GrappleGunAttachedHook, hookView.transform.position, destroyCancellationToken);
            }

            var floatTick = Runner.LocalRenderTime / Runner.DeltaTime;
            var startPosition = View.LineOriginPoint;
            var endPosition = HookData.hitPosition == Vector3.zero ? GetHookPosition(floatTick) : HookData.hitPosition;
            var rotation = Quaternion.LookRotation(HookData.fireVelocity);
            hookView.SetOrientation(startPosition, endPosition, rotation);
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            StopFire();
            DestroyHook();
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            var onFire = IsLeftInteractor ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onFire.Where(_ => HasStateAuthority && HasView).Subscribe(HandleFire).AddTo(DropCancellationToken);
        }

        protected override void HandleSelectExiting(SelectExitEventArgs args)
        {
            base.HandleSelectExiting(args);
            StopFire();
            DestroyHook();
        }

        private void HandleFire(InputAction.CallbackContext obj)
        {
            if (obj.started && !HookData.IsActive)
            {
                StartFire();
            }
            else if (obj.canceled && HookData.IsActive)
            {
                StopFire();
            }
        }

        private void StartFire()
        {
            HookData = new NetworkHookData
            {
                fireTick = Runner.Tick,
                firePosition = View.BarrelNode.position,
                fireVelocity = View.BarrelNode.forward * hookSpeed
            };
        }

        private void StopFire()
        {
            if (HookData.IsHooked)
            {
                onUnhooked.OnNext(new GrappleUnhookArgs(HandType));
            }

            HookData.Reset();
        }

        private void UpdateActiveState(int tick)
        {
            if (HookData.IsHooked)
            {
                if (HasView && HasHook)
                {
                    var canUnhook = (View.BarrelNode.position - hookView.LineAttachNode.position).sqrMagnitude < unhookSqrDistance;
                    if (canUnhook)
                    {
                        StopFire();
                    }
                }
            }
            else
            {
                var previousPosition = GetHookPosition(tick - 1);
                var nextPosition = GetHookPosition(tick);
                var direction = nextPosition - previousPosition;
                var distance = direction.magnitude;
                direction /= distance;

                if (Physics.Raycast(previousPosition, direction, out var hit, distance, hitMask))
                {
                    HookData.hitPosition = hit.point;
                    onHooked.OnNext(new GrappleHookArgs(HandType, hit.point));
                }
                else
                {
                    var currentSqrDistance = (HookData.firePosition - nextPosition).sqrMagnitude;
                    if (currentSqrDistance > maxHookSqrDistance)
                    {
                        StopFire();
                    }
                }
            }
        }

        private Vector3 GetHookPosition(float tick)
        {
            var time = (tick - HookData.fireTick) * Runner.DeltaTime;
            return time > 0 ? HookData.firePosition + HookData.fireVelocity * time : HookData.firePosition;
        }

        private void DestroyHook()
        {
            GunsManager.DestroyHook(hookView);
            hookView = null;
        }

        private struct NetworkHookData : INetworkStruct
        {
            public int fireTick;
            public Vector3 firePosition;
            public Vector3 fireVelocity;
            public Vector3 hitPosition;

            public bool IsActive => fireTick > 0 || IsHooked;
            public bool IsHooked => hitPosition != Vector3.zero;

            public void Reset()
            {
                fireTick = 0;
                firePosition = Vector3.zero;
                fireVelocity = Vector3.zero;
                hitPosition = Vector3.zero;
            }
        }
    }
}