using System;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.View.Locomotions;
using Modules.Core;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.Guns
{
    public class WebShooterActor : GunActor<WebShooterView>
    {
        [Header("WebShooter")]
        [SerializeField] private LineRenderer line;
        [SerializeField] private int raycastDistance;

        private readonly ISubject<WebHookArgs> onHooked = new Subject<WebHookArgs>();
        private readonly ISubject<WebUnhookArgs> onUnhooked = new Subject<WebUnhookArgs>();

        private bool isHooked;
        private Vector3 hookedPoint;

        public IObservable<WebHookArgs> OnHooked => onHooked;
        public IObservable<WebUnhookArgs> OnUnhooked => onUnhooked;

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            var onFire = IsLeftInteractor ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onFire.Where(_ => HasStateAuthority).Subscribe(HandleFire).AddTo(DropCancellationToken);
        }

        protected override void HandleSelectExiting(SelectExitEventArgs args)
        {
            base.HandleSelectExiting(args);
            TryUnhook();
        }

        private void LateUpdate()
        {
            if (isHooked && HasView)
            {
                line.SetPosition(0, View.BarrelNode.position);
                line.SetPosition(1, hookedPoint);
            }
        }

        private void HandleFire(InputAction.CallbackContext obj)
        {
            if (obj.started)
            {
                TryHook();
            }
            else if (obj.canceled)
            {
                TryUnhook();
            }
        }

        private void TryHook()
        {
            if (isHooked || !HasView || !Physics.Raycast(View.BarrelNode.position, View.BarrelNode.forward, out var hit, raycastDistance, 1 << Layers.Default))
            {
                return;
            }

            SendRpcSafe(() => HookRpc(hit.point));
        }

        private void TryUnhook()
        {
            if (!isHooked)
            {
                return;
            }

            SendRpcSafe(UnhookRpc);
        }

        private void PlayFireAudio()
        {
            if (!IsGrabbed || !PlayersModel.IsLocalPlayerInRadius(transform.position))
            {
                return;
            }

            AudioClient.Play(AudioKeys.WebShooterFire, transform.position, destroyCancellationToken);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void HookRpc(Vector3 hookedPoint)
        {
            isHooked = true;
            line.enabled = true;
            this.hookedPoint = hookedPoint;

            PlayFireAudio();

            if (HasStateAuthority)
            {
                onHooked.OnNext(new WebHookArgs(HandType, hookedPoint));
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void UnhookRpc()
        {
            isHooked = false;
            line.enabled = false;

            if (HasStateAuthority)
            {
                onUnhooked.OnNext(new WebUnhookArgs(HandType));
            }
        }
    }
}