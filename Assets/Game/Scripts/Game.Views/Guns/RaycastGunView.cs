using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using UnityEngine;

namespace Game.Views.Guns
{
    public class RaycastGunView : GunView
    {
        [SerializeField] private GameObject muzzleVfx;

        private void OnDisable()
        {
            muzzleVfx.SetActive(false);
        }

        public void ActivateMuzzleVfx()
        {
            muzzleVfx.SetActive(true);
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(0.1f))
                .Subscribe(_ => muzzleVfx.SetActive(false))
                .AddTo(destroyCancellationToken);
        }
    }
}