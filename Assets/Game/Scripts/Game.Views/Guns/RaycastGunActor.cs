using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.Projectiles;
using MessagePipe;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.Guns
{
    public class RaycastGunActor : GunActor<RaycastGunView>
    {
        [Header("RaycastGun")]
        [SerializeField] protected ProjectileId projectileId;
        [SerializeField] protected string fireAudioKey;
        [SerializeField] protected float fireRate;
        [SerializeField] protected bool autoFire;
        [SerializeField] private int damage;
        [SerializeField] private int initialAmmo;
        [SerializeField] private float maxDistance;
        [SerializeField] private AmmoIndicator ammoIndicator;

        private bool fireTriggered;
        private float lastFireTime;
        private int visibleFireCount;
        private ProjectilesManager projectilesManager;
        private IPublisher<RaycastGunDamageArgs> shootSignal;

        private Vector3 BarrelPosition => View.BarrelNode.position;
        private Vector3 BarrelDirection => View.BarrelNode.forward;

        public int Damage => damage;
        private bool HasAmmo => Ammo > 0;
        [Networked] private byte FireCount { get; set; }
        [Networked] [Capacity(8)] private NetworkArray<ProjectileData> ProjectileData { get; }
        [Networked] [OnChangedRender(nameof(ChangeAmmo))]
        public byte Ammo { get; private set; }

        [Inject]
        private void Construct(IPublisher<RaycastGunDamageArgs> shootSignal, ProjectilesManager projectilesManager)
        {
            this.shootSignal = shootSignal;
            this.projectilesManager = projectilesManager;
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            var isLeftHand = args.interactorObject.IsLeftHand();
            var onFire = isLeftHand ? XRInput.OnLeftActivate : XRInput.OnRightActivate;
            onFire.Where(_ => HasStateAuthority && HasView).Subscribe(HandleFireTriggered).AddTo(DropCancellationToken);

            ammoIndicator.SetActive(true);
            ammoIndicator.SetHandPosition(isLeftHand);
        }

        protected override void HandleSelectExited(SelectExitEventArgs args)
        {
            base.HandleSelectExited(args);
            fireTriggered = false;
            ammoIndicator.SetActive(false);
        }

        public override void Spawned()
        {
            base.Spawned();
            visibleFireCount = FireCount;
            ammoIndicator.SetActive(false);

            if (HasStateAuthority)
            {
                SetAmmo(initialAmmo);
            }

            ChangeAmmo();
        }

        public override void Render()
        {
            base.Render();
            if (!HasView)
            {
                return;
            }

            if (HasStateAuthority && fireTriggered)
            {
                Fire();

                if (!autoFire)
                {
                    fireTriggered = false;
                }
            }

            if (visibleFireCount < FireCount)
            {
                View.ActivateMuzzleVfx();
                AudioClient.Play(fireAudioKey, View.BarrelNode.position, destroyCancellationToken);
            }

            for (var i = visibleFireCount; i < FireCount; i++)
            {
                var data = ProjectileData[i % ProjectileData.Length];
                var projectile = projectilesManager.CreateProjectile(projectileId);
                projectile.Fire(BarrelPosition, data.targetPosition, data.impactType);
            }

            visibleFireCount = FireCount;
        }

        public void SetAmmo(int ammo)
        {
            Ammo = (byte)ammo;
        }

        private void HandleFireTriggered(InputAction.CallbackContext obj)
        {
            if (obj.started)
            {
                fireTriggered = true;
            }
            else if (obj.canceled)
            {
                fireTriggered = false;
            }
        }

        private void Fire()
        {
            if (Time.time - lastFireTime < fireRate)
            {
                return;
            }

            if (!HasAmmo)
            {
                AudioClient.Play(AudioKeys.NoAmmo, View.BarrelNode.position, destroyCancellationToken);
                lastFireTime = Time.time + 0.5f;
                return;
            }

            bool isEnemyHit;
            Vector3 targetPosition;

            if (Physics.Raycast(BarrelPosition, BarrelDirection, out var hit, maxDistance, hitMask))
            {
                targetPosition = hit.point;
                isEnemyHit = hit.collider.gameObject.layer is Layers.RemotePlayer or Layers.Enemy;
                shootSignal.Publish(new RaycastGunDamageArgs(this, hit));
            }
            else
            {
                isEnemyHit = false;
                targetPosition = BarrelPosition + BarrelDirection * maxDistance;
            }

            var impactType = isEnemyHit ? ProjectileImpactType.Enemy : ProjectileImpactType.Default;
            ProjectileData.Set(FireCount % ProjectileData.Length, new ProjectileData(targetPosition, impactType));
            FireCount++;
            Ammo = (byte)Mathf.Max(0, Ammo - 1);
            lastFireTime = Time.time;
        }

        private void ChangeAmmo()
        {
            ammoIndicator.SetAmmo(Ammo);
        }
    }
}