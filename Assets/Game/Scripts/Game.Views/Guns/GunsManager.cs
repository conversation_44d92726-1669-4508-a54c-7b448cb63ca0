using Game.Views.InteractablesCore;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Guns
{
    public class GunsManager : InteractablesManagerBase<GunData, GunsConfig, GunView>
    {
        [SerializeField] private GrappleGunHookView grappleGunHookPrefab;

        private IObjectResolver objectResolver;
        private ComponentPool<GrappleGunHookView> hookPool;

        [Inject]
        private void Construct(IObjectResolver objectResolver)
        {
            this.objectResolver = objectResolver;
        }

        public GrappleGunHookView CreateHook()
        {
            hookPool ??= new ComponentPool<GrappleGunHookView>(grappleGunHookPrefab, transform, objectResolver);
            return hookPool.Get();
        }

        public void DestroyHook(GrappleGunHookView view)
        {
            if (view == null)
            {
                return;
            }

            hookPool.Release(view);
        }
    }
}