using System.Collections.Generic;
using Game.Core.Extensions;
using Game.Views.InteractablesCore;
using UnityEngine;

namespace Game.Views.Guns
{
    public abstract class GunView : InteractableView
    {
        [SerializeField] private Transform collidersNode;
        [SerializeField] private Transform barrelNode;

        private readonly List<Renderer> rendererList = new();
        private float previewScaleMultiplier;

        public Transform BarrelNode => barrelNode;

        private void Awake()
        {
            transform.GetComponentsInChildren(true, rendererList);
            collidersNode.GetComponentsInChildren(true, AllColliderList);
            GameUtility.IgnoreCollisions(AllColliderList);
            previewScaleMultiplier = GetPreviewScaleMultiplier();
        }

        public override void SetPreviewState(float scale = 1)
        {
            base.SetPreviewState(scale * previewScaleMultiplier);
        }

        protected virtual float GetPreviewScaleMultiplier()
        {
            var value = GetBounds(rendererList).size.magnitude;
            return value == 0 ? 1 : 1 / value;
        }
    }
}