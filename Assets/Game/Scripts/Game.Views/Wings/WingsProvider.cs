using Game.Views.Avatars;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Wings
{
    public class WingsProvider : Actor
    {
        [SerializeField] private AvatarProvider avatarProvider;

        private WingsManager wingsManager;
        private WingsConfig wingsConfig;
        private WingsView view;
        private AvatarView avatarView;
        private bool hasAvatarView;
        private bool hasView;

        private Vector3 WingOffset => wingsConfig.WingOffset;
        private Vector3 WingRotation => wingsConfig.WingRotation;

        [Inject]
        private void Construct(WingsManager wingsManager, WingsConfig wingsConfig)
        {
            this.wingsConfig = wingsConfig;
            this.wingsManager = wingsManager;
        }

        private void LateUpdate()
        {
            UpdateView();
        }

        private void OnDestroy()
        {
            wingsManager.DestroyView(view);
            view = null;
            hasView = false;
        }

        public void SetAvatar(AvatarView avatarView)
        {
            this.avatarView = avatarView;
            hasAvatarView = avatarView != null;
        }

        public void ChangeWings(byte id)
        {
            wingsManager.DestroyView(view);
            wingsManager.TryCreateView(id, transform, out view);
            hasView = view != null;

            if (hasView)
            {
                view.SetAttachState(false);
            }
        }

        private void UpdateView()
        {
            if (!hasView || !hasAvatarView)
            {
                return;
            }

            view.UpdateLeftWing(GetShoulderPose(avatarView.LeftShoulderNode, avatarView.LeftElbowNode, true));
            view.UpdateRightWing(GetShoulderPose(avatarView.RightShoulderNode, avatarView.RightElbowNode, false));
        }

        private Pose GetShoulderPose(Transform shoulderNode, Transform elbowNode, bool isLeft)
        {
            var rotation = Quaternion.LookRotation(elbowNode.position - shoulderNode.position, elbowNode.up) * Quaternion.Euler(WingRotation);
            var offset = isLeft ? WingOffset : WingOffset.SetX(-WingOffset.x);
            var position = shoulderNode.position + rotation * offset;

            return new Pose(position, rotation);
        }
    }
}