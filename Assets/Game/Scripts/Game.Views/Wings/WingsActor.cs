using System.Collections.Generic;
using Fusion;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Wings
{
    public class WingsActor : InteractableActor
    {
        [SerializeField] protected Transform visualNode;
        [SerializeField] protected Collider selfCollider;
        [SerializeField] private string impactAudioKey;

        private PlayersModel playersModel;
        private IAudioClient audioClient;

        private bool CanCollide => HasStateAuthority && (IsGrabbed || (!IsGrabbed && Rigidbody.velocity.sqrMagnitude > 0.01f));

        protected WingsManager WingsManager { get; private set; }
        protected WingsConfig Config { get; private set; }

        [Inject]
        private void Construct(WingsManager wingsManager, WingsConfig wingsConfig, PlayersModel playersModel, IAudioClient audioClient)
        {
            this.playersModel = playersModel;
            this.audioClient = audioClient;
            WingsManager = wingsManager;
            Config = wingsConfig;
        }

        public override void Spawned()
        {
            base.Spawned();
            InitializeView();
            ChangeGrabber();
            ChangeIsAttached();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            UninitializeView();
        }

        protected override void ChangeGrabber()
        {
            base.ChangeGrabber();
            UpdateDropTimer(!IsGrabbed, DropTimeout);
            UpdateViewState();
        }

        protected override void SetColliders(List<Collider> colliderList)
        {
        }

        protected override void ClearColliders()
        {
        }

        protected virtual void UpdateViewState()
        {
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (!CanCollide)
            {
                return;
            }

            PlayImpactAudio();
        }

        private void PlayImpactAudio()
        {
            if (!HasStateAuthority || IsGrabbed)
            {
                return;
            }

            var playerList = playersModel.FindPlayers(transform.position);

            foreach (var player in playerList)
            {
                SendRpcSafe(() => PlayImpactAudioRpc(player.StateAuthority));
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void PlayImpactAudioRpc([RpcTarget] PlayerRef player)
        {
            var key = string.IsNullOrEmpty(impactAudioKey) ? AudioKeys.ImpactDefault : impactAudioKey;
            audioClient.Play(key, transform.position, destroyCancellationToken);
        }
    }

    public class WingsActor<TView> : WingsActor where TView : WingsView
    {
        private const float BoundsScale = 0.4f;

        protected TView View { get; private set; }
        protected bool HasView => View != null;

        protected override void InitializeView()
        {
            base.InitializeView();

            if (WingsManager.TryCreateView(InteractableId, visualNode, out var view) && view is TView currentView)
            {
                View = currentView;
            }
            else
            {
                WingsManager.DestroyView(view);
            }

            InteractableCode = Config.GetInteractableData(InteractableId)?.Code;
            InitializeGrabbing(View);
            UpdateViewState();
        }

        protected override void UninitializeView()
        {
            base.UninitializeView();
            UninitializeGrabbing();
            WingsManager?.DestroyView(View);
            View = null;
        }

        protected override void UpdateViewState()
        {
            if (!HasView)
            {
                return;
            }

            if (IsGrabbed)
            {
                View.SetGrabState(HasStateAuthority, BoundsScale);
                selfCollider.enabled = HasStateAuthority;
            }
            else
            {
                View.SetDropState(true, BoundsScale);
                selfCollider.enabled = true;
            }
        }
    }
}