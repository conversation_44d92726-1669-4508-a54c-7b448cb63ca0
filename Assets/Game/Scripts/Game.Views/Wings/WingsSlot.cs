using DG.Tweening;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Wings
{
    public class WingsSlot : Actor
    {
        [SerializeField] private Transform viewNode;
        [SerializeField] private Material previewMaterial;

        private WingsManager wingsManager;
        private WingsView wingsView;
        private Sequence sequence;

        private void OnDestroy()
        {
            DestroyWingsView();
        }

        private void OnDisable()
        {
            sequence?.Kill();
            transform.localScale = Vector3.one;
        }

        public override void SetActive(bool isActive)
        {
            if (!isActive)
            {
                DestroyWingsView();
            }

            base.SetActive(isActive);
        }

        public void Initialize(WingsManager wingsManager)
        {
            this.wingsManager = wingsManager;
        }

        public void Show(byte wingId)
        {
            DestroyWingsView();
            CreateWingsView(wingId);
            SetGhostView();
            Show();
        }

        public void SetDefaultView()
        {
            wingsView.RevertCustomMaterial();
        }

        public void SetGhostView()
        {
            wingsView.ApplyCustomMaterial(previewMaterial);
        }

        public void SetHoverStartState()
        {
            sequence?.Kill();
            sequence = DOTween.Sequence();
            sequence.Append(transform.DOScale(1.5f * Vector3.one, 0.25f));
        }

        public void SetHoverEndState()
        {
            sequence?.Kill();
            sequence = DOTween.Sequence();
            sequence.Append(transform.DOScale(Vector3.one, 0.25f));
        }

        private void CreateWingsView(byte wingId)
        {
            wingsManager.TryCreateView(wingId, viewNode, out wingsView);
            wingsView.SetPreviewState(0.1f);
        }

        private void DestroyWingsView()
        {
            if (wingsView == null)
            {
                return;
            }

            wingsView.RevertCustomMaterial();
            wingsManager.DestroyView(wingsView);
            wingsView = null;
        }
    }
}