using System.Collections.Generic;
using Game.Views.Interactables;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.GadgetShop
{
    public class GadgetShopConfig : Config
    {
        [SerializeField] private List<GadgetShopData> gadgetShopDataList;

        private InteractablesConfig interactablesConfig;

        public List<GadgetShopData> GadgetShopDataList => gadgetShopDataList;

        [Inject]
        private void Construct(InteractablesConfig interactablesConfig)
        {
            this.interactablesConfig = interactablesConfig;
        }

        public bool TryGetGadgetShopData(int interactableId, out GadgetShopData gadgetShopData)
        {
            var interactableData = interactablesConfig.GetInteractableData(interactableId);
            if (interactableData == null)
            {
                gadgetShopData = null;
                return false;
            }

            gadgetShopData = gadgetShopDataList.Find(x => x.viewCode == interactableData.Code);
            return gadgetShopData != null;
        }
    }
}