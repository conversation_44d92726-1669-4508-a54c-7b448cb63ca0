using System;
using System.Reactive.Subjects;
using DG.Tweening;
using Game.Views.Interactables;
using Game.Views.Shared;
using Modules.XR;
using TMPro;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;
using XRSimpleInteractable = UnityEngine.XR.Interaction.Toolkit.XRSimpleInteractable;

namespace Game.Views.GadgetShop
{
    public class GadgetInventoryView : XRSimpleInteractable
    {
        [Header("GadgetInventoryView")]
        [SerializeField] private TMP_Text nameText;
        [SerializeField] private TMP_Text priceText;
        [SerializeField] private GameObject textsNode;
        [SerializeField] private Transform interactableNode;
        [SerializeField] private InteractableProvider interactableProvider;
        [SerializeField] private float grabEndThreshold = 0.5f;

        private readonly ISubject<GadgetInventoryView> onClicked = new Subject<GadgetInventoryView>();

        private IXRInput xrInput;
        private Tweener scaleTweener;
        private Vector3 grabDeltaPosition;
        private Vector3 grabInitialPosition;
        private Vector3 originLocalPosition;
        private DistanceCuller distanceCuller;

        public GadgetShopData Data { get; private set; }
        public IXRSelectInteractor GrabInteractor { get; private set; }
        public IObservable<GadgetInventoryView> OnClicked => onClicked;

        [Inject]
        private void Construct(IXRInput xrInput, DistanceCuller distanceCuller)
        {
            this.xrInput = xrInput;
            this.distanceCuller = distanceCuller;
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            distanceCuller.AddTarget(textsNode);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ResetGrabbing();
            distanceCuller.RemoveTarget(textsNode);
        }

        private void Update()
        {
            UpdateGrabbing();
        }

        protected override void OnHoverEntered(HoverEnterEventArgs args)
        {
            base.OnHoverEntered(args);
            interactableNode.DOScale(1.2f, 0.2f).SetEase(Ease.OutBack);
            xrInput.SendHapticImpulse(args.interactorObject.GetHandType(), HapticImpulse.ShortDurationLowAmplitude);
        }

        protected override void OnHoverExited(HoverExitEventArgs args)
        {
            base.OnHoverExited(args);
            interactableNode.DOScale(1, 0.2f).SetEase(Ease.OutBack);
        }

        protected override void OnSelectEntered(SelectEnterEventArgs args)
        {
            base.OnSelectEntered(args);
            GrabInteractor = args.interactorObject;
            grabInitialPosition = GrabInteractor.transform.position;
            grabDeltaPosition = interactableNode.position - grabInitialPosition;
        }

        protected override void OnSelectExited(SelectExitEventArgs args)
        {
            base.OnSelectExited(args);
            ResetGrabbing();
        }

        public void Render(GadgetShopData data, float offset = 0)
        {
            if (data == null || data == Data)
            {
                return;
            }

            Clear();

            Data = data;
            interactableProvider.Create(data.viewCode, 1);
            transform.localPosition = new Vector3(offset, 0, 0);
            nameText.text = data.title;
            priceText.text = $"{data.diamondPrice} <sprite name=Diamond>";
        }

        private void Clear()
        {
            interactableProvider.Destroy();
        }

        private void Click()
        {
            if (Data == null)
            {
                return;
            }

            onClicked.OnNext(this);
        }

        private void UpdateGrabbing()
        {
            if (GrabInteractor == null)
            {
                return;
            }

            var grabCurrentPosition = GrabInteractor.transform.position;
            var distance = Vector3.Distance(grabInitialPosition, grabCurrentPosition);

            if (distance > grabEndThreshold)
            {
                Click();
                ResetGrabbing();
            }
            else
            {
                interactableNode.position = grabCurrentPosition + grabDeltaPosition;
            }
        }

        private void ResetGrabbing()
        {
            GrabInteractor = null;
            interactableNode.localPosition = Vector3.zero;
        }
    }
}