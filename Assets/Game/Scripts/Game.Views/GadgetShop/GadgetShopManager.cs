using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.GadgetShop
{
    public class GadgetShopManager : Actor
    {
        [SerializeField] private GadgetShopView gadgetShopViewPrefab;

        private IObjectResolver objectResolver;
        private GadgetShopConfig gadgetShopConfig;
        private ComponentPool<GadgetShopView> gadgetShopViewPool;
        private CancellationTokenSource gadgetShopViewCancellationTokenSource;

        private readonly ISubject<GadgetInventoryView> onClicked = new Subject<GadgetInventoryView>();

        public IObservable<GadgetInventoryView> OnClicked => onClicked;

        [Inject]
        private void Construct(IObjectResolver objectResolver, GadgetShopConfig gadgetShopConfig)
        {
            this.objectResolver = objectResolver;
            this.gadgetShopConfig = gadgetShopConfig;
        }

        private void Awake()
        {
            gadgetShopViewPool = new ComponentPool<GadgetShopView>(gadgetShopViewPrefab, transform, objectResolver, 0);
        }

        public void ClearInstances()
        {
            gadgetShopViewCancellationTokenSource.CancelAndDispose();
            gadgetShopViewCancellationTokenSource = null;
            gadgetShopViewPool.ReleaseAll();
        }

        public void CreateInstance(Pose pose)
        {
            gadgetShopViewCancellationTokenSource ??= new CancellationTokenSource();

            var view = gadgetShopViewPool.Get();
            view.SetPose(pose);
            view.Initialize(gadgetShopConfig.GadgetShopDataList.FindAll(g => !g.disabledInShop));
            view.OnClicked.Subscribe(HandleClicked).AddTo(gadgetShopViewCancellationTokenSource.Token);
        }

        private void HandleClicked(GadgetInventoryView gadgetInventoryView)
        {
            onClicked.OnNext(gadgetInventoryView);
        }
    }
}