using System.Collections.Generic;
using Modules.Core;
using UnityEngine;

namespace Game.Views.InteractablesCore
{
    public abstract class InteractableView : Actor
    {
        [SerializeField] private Transform leftHandAttachNode;
        [SerializeField] private Transform rightHandAttachNode;
        [SerializeField] protected Transform centerAttachNode;

        public string Code { get; private set; } = string.Empty;
        public Transform LeftHandAttachNode => leftHandAttachNode;
        public Transform RightHandAttachNode => rightHandAttachNode;
        public List<Collider> AllColliderList { get; } = new();

        public virtual void Initialize(string code, Transform parent)
        {
            Code = code;
            SetParent(parent);
            transform.SetLocalPositionAndRotation(Vector3.zero, Quaternion.identity);
        }

        public virtual void Uninitialize()
        {
            Code = string.Empty;
        }

        public virtual void SetAttachState(bool isActiveColliders, float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(isActiveColliders);
        }

        public virtual void SetGrabState(bool isActiveColliders, float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(isActiveColliders);
        }

        public virtual void SetEquipState(float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(false);
        }

        public virtual void SetDropState(bool isActiveColliders, float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(isActiveColliders);
        }

        public virtual void SetPreviewState(float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(false);
            AttachToCenter(transform.parent);
        }

        public virtual void SetShopState(float scale = 1)
        {
            SetScale(scale);
            SetActiveAllColliderList(false);
            AttachToCenter(transform.parent);
        }

        public virtual void ApplyCustomMaterial(Material customMaterial)
        {
        }

        public virtual void RevertCustomMaterial()
        {
        }

        protected void SetActiveAllColliderList(bool isActive)
        {
            AllColliderList.ForEach(c => c.enabled = isActive);
        }

        protected void AttachToCenter(Transform parent = null)
        {
            transform.SetParent(parent, false);
            transform.SetLocalPositionAndRotation(Vector3.zero, Quaternion.identity);

            var position = centerAttachNode.InverseTransformPoint(transform.position) * transform.localScale.x;
            var rotation = Quaternion.Inverse(centerAttachNode.localRotation);
            transform.SetLocalPositionAndRotation(position, rotation);
        }

        protected void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        protected Bounds GetBounds(List<Renderer> rendererList)
        {
            if (rendererList.Count == 0)
            {
                return default;
            }

            var firstRenderer = rendererList[0];

            if (rendererList.Count == 1)
            {
                return firstRenderer.bounds;
            }

            var bounds = firstRenderer.bounds;
            rendererList.FindAll(r => r != firstRenderer).ForEach(r => bounds.Encapsulate(r.bounds));
            return bounds;
        }
    }
}