using System;
using System.Collections.Generic;
using Game.Core;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.InteractablesCore
{
    public class InteractablesManagerBase<TData, TConfig, TView> : MonoBehaviour
        where TData : InteractableData
        where TConfig : InteractablesConfigBase<TData>
        where TView : InteractableView
    {
        private readonly Dictionary<string, ComponentPool<TView>> viewPoolList = new();

        protected TConfig Config { get; private set; }
        protected IObjectResolver ObjectResolver { get; private set; }
        protected INetworkClient NetworkClient { get; private set; }

        [Inject]
        private void Construct(TConfig config, INetworkClient networkClient, IObjectResolver objectResolver)
        {
            Config = config;
            NetworkClient = networkClient;
            ObjectResolver = objectResolver;
        }

        protected virtual void OnDestroy()
        {
            viewPoolList.Clear();
        }

        public bool TryCreateView(int id, Transform parent, out TView view)
        {
            view = CreateView(Config.GetInteractableData(id), parent);
            return view != null;
        }

        public bool TryCreateView(string code, Transform parent, out TView view)
        {
            view = CreateView(Config.GetInteractableData(code), parent);
            return view != null;
        }

        public InteractableActor CreateActor(string code, Vector3 position, Quaternion rotation, Action<InteractableActor> onBeforeCreated = null)
        {
            var data = Config.GetInteractableData(code);
            return CreateActor(data, position, rotation, onBeforeCreated);
        }

        public InteractableActor CreateActor(int id, Vector3 position, Quaternion rotation, Action<InteractableActor> onBeforeCreated = null)
        {
            var data = Config.GetInteractableData(id);
            return CreateActor(data, position, rotation, onBeforeCreated);
        }

        public InteractableActor CreateActor(string code, Pose pose, Action<InteractableActor> onBeforeCreated = null)
        {
            return CreateActor(code, pose.position, pose.rotation, onBeforeCreated);
        }

        public InteractableActor CreateActor(int id, Pose pose, Action<InteractableActor> onBeforeCreated = null)
        {
            return CreateActor(id, pose.position, pose.rotation, onBeforeCreated);
        }

        public virtual bool TryCreateActor(string code, Pose pose, Action<InteractableActor> onBeforeCreated, out InteractableActor actor)
        {
            actor = CreateActor(code, pose, onBeforeCreated);
            return actor != null;
        }

        public virtual bool TryCreateActor(int id, Pose pose, Action<InteractableActor> onBeforeCreated, out InteractableActor actor)
        {
            actor = CreateActor(id, pose, onBeforeCreated);
            return actor != null;
        }

        public bool TryCreateActor(string code, Pose pose, out InteractableActor actor)
        {
            return TryCreateActor(code, pose, null, out actor);
        }

        public bool TryCreateActor(int id, Pose pose, out InteractableActor actor)
        {
            return TryCreateActor(id, pose, null, out actor);
        }

        public bool DestroyView(InteractableView view)
        {
            if (view is not TView tView || !viewPoolList.TryGetValue(tView.Code, out var pool))
            {
                return false;
            }

            tView.Uninitialize();
            pool.Release(tView);
            return true;
        }

        public void DestroyActor(NetworkActor actor)
        {
            if (actor == null || actor.Object == null)
            {
                return;
            }

            NetworkClient.Despawn(actor.Object);
        }

        private TView CreateView(TData data, Transform parent, Action<TView> onCreated = null)
        {
            if (data == null)
            {
                return null;
            }

            if (!viewPoolList.ContainsKey(data.Code))
            {
                viewPoolList[data.Code] = new ComponentPool<TView>(data.ViewPrefab as TView, transform, ObjectResolver, 0);
            }

            var view = viewPoolList[data.Code].Get();
            view.Initialize(data.Code, parent);
            onCreated?.Invoke(view);

            return view;
        }

        private InteractableActor CreateActor(InteractableData data, Vector3 position, Quaternion rotation, Action<InteractableActor> onBeforeCreated = null)
        {
            if (data == null)
            {
                return null;
            }

            var prefab = data.ActorPrefab;
            if (prefab == null)
            {
                GameLogger.Interactables.Warn("Interactable actor prefab is null. Code: {0}", data.Code);
                return null;
            }

            InteractableActor actor = null;
            NetworkClient.Spawn(prefab, position, rotation, (_, obj) =>
            {
                actor = obj.GetComponent<InteractableActor>();
                if (actor != null)
                {
                    actor.InteractableId = (byte)data.Id;
                }

                onBeforeCreated?.Invoke(actor);
            });

            return actor;
        }
    }
}