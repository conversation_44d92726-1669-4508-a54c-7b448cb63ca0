using Fusion;
using Game.Views.Grabbing;
using Modules.Core;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.InteractablesCore
{
    public abstract class InteractableActor : GrabbableItem
    {
        private SharedInteractableSettings sharedInteractableSettings;
        private IInteractableInteractionPublisher interactableInteractionPublisher;

        [Networked] public byte InteractableId { get; set; }
        public string InteractableCode { get; protected set; }

        [Inject]
        private void Construct(IInteractableInteractionPublisher interactableInteractionPublisher, SharedInteractableSettings sharedInteractableSettings)
        {
            this.sharedInteractableSettings = sharedInteractableSettings;
            this.interactableInteractionPublisher = interactableInteractionPublisher;
        }

        public override void Spawned()
        {
            base.Spawned();
            SetScale(CoreConstants.WorldScale);
            DropTimeout = sharedInteractableSettings.DropTimeout;
        }

        protected virtual void InitializeView()
        {
            UninitializeView();
        }

        protected virtual void UninitializeView()
        {
        }

        protected void InitializeGrabbing(InteractableView view)
        {
            if (view != null)
            {
                SetLeftHandAttachNode(view.LeftHandAttachNode);
                SetRightHandAttachNode(view.RightHandAttachNode);
                SetColliders(view.AllColliderList);
            }

            RegisterInteractable();
        }

        protected void UninitializeGrabbing()
        {
            UnregisterInteractable();
            SetLeftHandAttachNode(null);
            SetRightHandAttachNode(null);
            ClearColliders();
        }

        protected override void HandleHoverEntered(HoverEnterEventArgs args)
        {
            base.HandleHoverEntered(args);
            interactableInteractionPublisher.Publish(this, args);
        }

        protected override void HandleHoverExited(HoverExitEventArgs args)
        {
            base.HandleHoverExited(args);
            interactableInteractionPublisher.Publish(this, args);
        }

        protected override void HandleSelectEntered(SelectEnterEventArgs args)
        {
            base.HandleSelectEntered(args);
            interactableInteractionPublisher.Publish(this, args);
        }

        protected override void HandleSelectExited(SelectExitEventArgs args)
        {
            base.HandleSelectExited(args);
            interactableInteractionPublisher.Publish(this, args);
        }
    }
}