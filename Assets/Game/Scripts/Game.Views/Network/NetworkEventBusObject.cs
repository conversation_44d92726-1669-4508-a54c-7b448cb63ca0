using System.Reactive;
using Fusion;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Network
{
    public class NetworkEventBusObject : NetworkActor
    {
        private NetworkEventBus networkEventBus;

        [Networked] [OnChangedRender(nameof(ChangeIsGameRunning))]
        private NetworkBool IsGameRunning { get; set; }

        [Networked] [OnChangedRender(nameof(ChangeIsBuildingDisabled))]
        private NetworkBool IsBuildingDisabled { get; set; }

        [Inject]
        private void Construct(NetworkEventBus networkEventBus)
        {
            this.networkEventBus = networkEventBus;
        }

        public override void Spawned()
        {
            base.Spawned();
            ChangeIsGameRunning();
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();

            networkEventBus.onFixedUpdateNetwork.OnNext(Unit.Default);
        }

        private void ChangeIsGameRunning()
        {
            if (networkEventBus.isGameRunning.Value != IsGameRunning)
            {
                networkEventBus.isGameRunning.Value = IsGameRunning;
            }
        }

        private void ChangeIsBuildingDisabled()
        {
            if (networkEventBus.isBuildingDisabled.Value != IsBuildingDisabled)
            {
                networkEventBus.isBuildingDisabled.Value = IsBuildingDisabled;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SetIsGameRunningRpc(bool isGameRunning)
        {
            networkEventBus.onGameRunningUpdated.OnNext(isGameRunning);

            if (HasStateAuthority)
            {
                IsGameRunning = isGameRunning;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SetIsBuildingDisabledRpc(bool isBuildingDisabled)
        {
            networkEventBus.onBuildingDisabledUpdated.OnNext(isBuildingDisabled);

            if (HasStateAuthority)
            {
                IsBuildingDisabled = isBuildingDisabled;
            }
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SendMapSavedRpc()
        {
            networkEventBus.onMapSaved.OnNext(Unit.Default);
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SendLevelFriendRequestRpc([RpcTarget] PlayerRef player, RpcInfo info = default)
        {
            networkEventBus.onLevelFriendRequestReceived.OnNext(info.Source);
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SendLevelFriendResponseRpc([RpcTarget] PlayerRef playerRef, bool ok, RpcInfo info = default)
        {
            networkEventBus.onLevelFriendResponseReceived.OnNext((info.Source, ok));
        }

        [Rpc(RpcSources.All, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        public void SendEffectSpawnRpc([RpcTarget] PlayerRef playerRef, byte code, Vector3 position, RpcInfo info = default)
        {
            networkEventBus.onEffectSpawnReceived.OnNext((info.Source, code, position));
        }

        [Rpc(RpcSources.All, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        public void SendRaceEndedRpc([RpcTarget] PlayerRef playerRef, string raceId, RpcInfo info = default)
        {
            networkEventBus.onRaceEndReceived.OnNext((info.Source, raceId));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SendKillCurrentSessionRpc()
        {
            networkEventBus.onKillCurrentSessionReceived.OnNext(Unit.Default);
        }

        [ContextMenu("Kill Current Session")]
        private void KillCurrentSession()
        {
            SendKillCurrentSessionRpc();
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        public void SendConsumeCoinsRpc([RpcTarget] PlayerRef playerRef, int coinAmount)
        {
            networkEventBus.onConsumeCoinsReceived.OnNext(coinAmount);
        }
    }
}