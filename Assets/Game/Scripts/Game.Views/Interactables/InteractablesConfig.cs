using System.Collections.Generic;
using System.Linq;
using Game.Views.Badges;
using Game.Views.Consumeables;
using Game.Views.Guns;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Weapons;
using Game.Views.Wings;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Interactables
{
    public class InteractablesConfig : Config
    {
        private GunsConfig gunsConfig;
        private WingsConfig wingsConfig;
        private ItemsConfig itemsConfig;
        private BadgesConfig badgesConfig;
        private WeaponsConfig weaponsConfig;
        private ConsumablesConfig consumablesConfig;

        [Inject]
        private void Construct(
            GunsConfig gunsConfig,
            WingsConfig wingsConfig,
            ItemsConfig itemsConfig,
            BadgesConfig badgesConfig,
            WeaponsConfig weaponsConfig,
            ConsumablesConfig consumablesConfig)
        {
            this.gunsConfig = gunsConfig;
            this.wingsConfig = wingsConfig;
            this.itemsConfig = itemsConfig;
            this.badgesConfig = badgesConfig;
            this.weaponsConfig = weaponsConfig;
            this.consumablesConfig = consumablesConfig;
        }

        public IEnumerable<InteractableData> GetInteractableDataList()
        {
            foreach (var item in weaponsConfig.InteractablesDataList)
            {
                yield return item;
            }

            foreach (var item in gunsConfig.InteractablesDataList)
            {
                yield return item;
            }

            foreach (var item in itemsConfig.InteractablesDataList)
            {
                yield return item;
            }

            foreach (var item in wingsConfig.InteractablesDataList)
            {
                yield return item;
            }

            foreach (var item in consumablesConfig.InteractablesDataList)
            {
                yield return item;
            }

            foreach (var item in badgesConfig.InteractablesDataList)
            {
                yield return item;
            }
        }

        public InteractableData GetInteractableData(string code)
        {
            foreach (var interactableData in GetInteractableDataList())
            {
                if (interactableData.Code == code)
                {
                    return interactableData;
                }
            }

            return null;
        }

        public InteractableData GetInteractableData(int id)
        {
            foreach (var interactableData in GetInteractableDataList())
            {
                if (interactableData.Id == id)
                {
                    return interactableData;
                }
            }

            return null;
        }

        public bool Has(string code)
        {
            foreach (var interactableData in GetInteractableDataList())
            {
                if (interactableData.Code == code)
                {
                    return true;
                }
            }

            return false;
        }

        public bool IsWeapon(string code)
        {
            return weaponsConfig.Has(code);
        }

        public bool IsGun(string code)
        {
            return gunsConfig.Has(code);
        }

        public bool IsWing(string code)
        {
            return wingsConfig.Has(code);
        }

        public bool IsItem(string code)
        {
            return itemsConfig.Has(code);
        }

#if UNITY_EDITOR
        [Sirenix.OdinInspector.Button("Validate all interactable id")]
        private void ValidateInteractablesId()
        {
            var gunsConfig = LoadAssetOfType<GunsConfig>();
            var wingsConfig = LoadAssetOfType<WingsConfig>();
            var itemsConfig = LoadAssetOfType<ItemsConfig>();
            var badgesConfig = LoadAssetOfType<BadgesConfig>();
            var weaponsConfig = LoadAssetOfType<WeaponsConfig>();
            var consumablesConfig = LoadAssetOfType<ConsumablesConfig>();
            var interactableDataList = new List<InteractableData>();
            Populate(interactableDataList, gunsConfig.InteractablesDataList);
            Populate(interactableDataList, wingsConfig.InteractablesDataList);
            Populate(interactableDataList, itemsConfig.InteractablesDataList);
            Populate(interactableDataList, badgesConfig.InteractablesDataList);
            Populate(interactableDataList, weaponsConfig.InteractablesDataList);
            Populate(interactableDataList, consumablesConfig.InteractablesDataList);

            var id = 1;

            foreach (var interactableData in interactableDataList)
            {
                interactableData.SetId(id++);
            }

            UnityEditor.EditorUtility.SetDirty(gunsConfig);
            UnityEditor.EditorUtility.SetDirty(wingsConfig);
            UnityEditor.EditorUtility.SetDirty(itemsConfig);
            UnityEditor.EditorUtility.SetDirty(badgesConfig);
            UnityEditor.EditorUtility.SetDirty(weaponsConfig);
            UnityEditor.EditorUtility.SetDirty(consumablesConfig);
            UnityEditor.AssetDatabase.SaveAssets();
        }

        private void Populate<T>(List<InteractableData> source, List<T> target) where T : InteractableData
        {
            if (target is { Count: > 0 })
            {
                source.AddRange(target);
            }
        }

        private static T LoadAssetOfType<T>() where T : Object
        {
            return UnityEditor.AssetDatabase
                .FindAssets($"t:{typeof(T).Name}")
                .Select(UnityEditor.AssetDatabase.GUIDToAssetPath)
                .Select(UnityEditor.AssetDatabase.LoadAssetAtPath<T>)
                .FirstOrDefault();
        }
#endif
    }
}